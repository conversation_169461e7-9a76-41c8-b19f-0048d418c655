package model

import (
	"time"
)

// AlbumContent 合集内容关联模型
type AlbumContent struct {
	ID           uint64     `gorm:"primaryKey;autoIncrement;not null" json:"id" comment:"自增主键"`
	AlbumKSUID   string     `gorm:"column:album_ksuid;type:varchar(32);not null;index" json:"album_ksuid" comment:"合集KSUID"`
	ContentKSUID string     `gorm:"column:content_ksuid;type:varchar(32);not null;index" json:"content_ksuid" comment:"内容KSUID"`
	ContentType  string     `gorm:"column:content_type;type:varchar(20);not null" json:"content_type" comment:"内容类型"`
	SortOrder    int        `gorm:"column:sort_order;type:int;default:0" json:"sort_order" comment:"排序顺序"`
	CreatedAt    time.Time  `gorm:"column:created_at;not null" json:"created_at" comment:"创建时间"`
	UpdatedAt    time.Time  `gorm:"column:updated_at;not null" json:"updated_at" comment:"更新时间"`
	DeletedAt    *time.Time `gorm:"column:deleted_at;index" json:"deleted_at,omitempty" comment:"删除时间"`
}

func (AlbumContent) TableName() string {
	return "interaction_album_contents"
}
