package main

import (
	"context"
	"fmt"
	"go.uber.org/fx"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/help-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	// 基础设施 providers
	"pxpat-backend/cmd/content-cluster/help-service/providers"

	// 业务层 providers
	"pxpat-backend/internal/content-cluster/help-service/handler"
	"pxpat-backend/internal/content-cluster/help-service/repository"
	"pxpat-backend/internal/content-cluster/help-service/service"
)

// ManageLifecycle 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(context.Background()); err != nil {
				fmt.Println("here")
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Msg("帮助中心服务启动中...")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry
			if err := otelProvider.Shutdown(ctx); err != nil {
				log.Error().Err(err).Msg("关闭 OpenTelemetry 失败")
			} else {
				log.Info().Msg("OpenTelemetry 关闭成功")
			}

			return nil
		},
	})

	log.Info().Msg("应用生命周期管理初始化成功")
}

func main() {
	app := fx.New(
		// 基础设施层
		fx.Provide(
			providers.ProvideConfig,
			providers.ProvideOtelProvider,
			providers.ProvideDatabase,
			providers.ProvideConsulManager,
			providers.ProvideHealthHandler,
		),

		// 存储层
		fx.Provide(
			repository.ProvideHelpRepository, // 存储层
		),

		// 业务层
		fx.Provide(
			service.ProvideHelpService,
		),

		// 处理器层
		fx.Provide(
			handler.ProvideHelpHandler,
		),

		// 应用层
		fx.Provide(
			providers.ProvideGinEngine,
		),

		// 生命周期管理
		fx.Invoke(
			providers.ProvideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
