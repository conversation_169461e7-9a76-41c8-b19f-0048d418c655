package client

import (
	"fmt"
	"time"

	"pxpat-backend/pkg/httpclient"
	globalTypes "pxpat-backend/pkg/types"

	"github.com/rs/zerolog/log"
)

// ContentStorageServiceClient 存储服务客户端接口
type ContentStorageServiceClient interface {
	// GetCoverInfo 根据封面KSUID获取封面信息（内部调用）
	GetCoverInfo(coverKSUID string) (*CoverInfoResponse, error)
}

// contentStorageServiceClient 存储服务客户端实现
type contentStorageServiceClient struct {
	httpClient *httpclient.HTTPClient
}

// ContentStorageServiceConfig 存储服务客户端配置
type ContentStorageServiceConfig struct {
	BaseURL string
	Timeout time.Duration
}

// CoverInfoResponse 封面信息响应
type CoverInfoResponse struct {
	CoverKSUID string `json:"cover_ksuid"` // 封面唯一ID
	UserKSUID  string `json:"user_ksuid"`  // 所属用户的ID
	Filename   string `json:"file_name"`   // 用户上传时的原始文件名
	Ext        string `json:"ext"`         // 文件扩展名
	BucketPath string `json:"bucket_path"` // 文件在存储系统中的实际路径或对象键名
	FileSize   int64  `json:"file_size"`   // 文件大小 (单位: 字节)
	MimeType   string `json:"mime_type"`   // 文件的MIME类型 (例如: image/jpeg, image/png)
	Status     string `json:"status"`      // 文件状态 (例如: pending, uploaded, active, deleted)
	Hash       string `json:"hash"`        // 文件哈希值 (JSON格式存储SHA-256)
	Width      int    `json:"width"`       // 图片宽度
	Height     int    `json:"height"`      // 图片高度
	FileURL    string `json:"file_url"`    // 文件访问URL
}

// NewContentStorageServiceClient 创建存储服务客户端
func NewContentStorageServiceClient(config ContentStorageServiceConfig) ContentStorageServiceClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second // 默认超时
	}

	log.Info().
		Str("base_url", config.BaseURL).
		Dur("timeout", config.Timeout).
		Msg("Creating content storage service client")

	httpClient := httpclient.NewHTTPClient(httpclient.ClientConfig{
		BaseURL:          config.BaseURL,
		Timeout:          config.Timeout,
		RetryCount:       3,
		RetryWaitTime:    1 * time.Second,
		RetryMaxWaitTime: 5 * time.Second,
	})

	log.Info().
		Str("base_url", config.BaseURL).
		Msg("Content storage service client created successfully")

	return &contentStorageServiceClient{
		httpClient: httpClient,
	}
}

// GetCoverInfo 根据封面KSUID获取封面信息
func (c *contentStorageServiceClient) GetCoverInfo(coverKSUID string) (*CoverInfoResponse, error) {
	log.Debug().
		Str("cover_ksuid", coverKSUID).
		Msg("调用存储服务获取封面信息")

	var response globalTypes.GlobalResponse
	requestURL := fmt.Sprintf("/internal/v1/intra/cover/%s", coverKSUID)

	err := c.httpClient.Get(requestURL, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("cover_ksuid", coverKSUID).
			Str("url", requestURL).
			Msg("调用存储服务获取封面信息API失败")
		return nil, fmt.Errorf("failed to call storage service get cover info API: %w", err)
	}

	if response.Code < 0 {
		log.Warn().
			Str("cover_ksuid", coverKSUID).
			Int("response_code", response.Code).
			Msg("存储服务返回错误")
		return nil, fmt.Errorf("storage service returned error code: %d", response.Code)
	}

	// 将 response.Data 转换为 CoverInfoResponse
	coverInfo, ok := response.Data.(map[string]interface{})
	if !ok {
		log.Error().
			Str("cover_ksuid", coverKSUID).
			Msg("存储服务返回数据格式错误")
		return nil, fmt.Errorf("invalid response data format")
	}

	result := &CoverInfoResponse{}
	
	// 安全地提取字段
	if val, exists := coverInfo["cover_ksuid"]; exists {
		if str, ok := val.(string); ok {
			result.CoverKSUID = str
		}
	}
	if val, exists := coverInfo["user_ksuid"]; exists {
		if str, ok := val.(string); ok {
			result.UserKSUID = str
		}
	}
	if val, exists := coverInfo["file_name"]; exists {
		if str, ok := val.(string); ok {
			result.Filename = str
		}
	}
	if val, exists := coverInfo["ext"]; exists {
		if str, ok := val.(string); ok {
			result.Ext = str
		}
	}
	if val, exists := coverInfo["bucket_path"]; exists {
		if str, ok := val.(string); ok {
			result.BucketPath = str
		}
	}
	if val, exists := coverInfo["file_size"]; exists {
		if num, ok := val.(float64); ok {
			result.FileSize = int64(num)
		}
	}
	if val, exists := coverInfo["mime_type"]; exists {
		if str, ok := val.(string); ok {
			result.MimeType = str
		}
	}
	if val, exists := coverInfo["status"]; exists {
		if str, ok := val.(string); ok {
			result.Status = str
		}
	}
	if val, exists := coverInfo["hash"]; exists {
		if str, ok := val.(string); ok {
			result.Hash = str
		}
	}
	if val, exists := coverInfo["width"]; exists {
		if num, ok := val.(float64); ok {
			result.Width = int(num)
		}
	}
	if val, exists := coverInfo["height"]; exists {
		if num, ok := val.(float64); ok {
			result.Height = int(num)
		}
	}
	if val, exists := coverInfo["file_url"]; exists {
		if str, ok := val.(string); ok {
			result.FileURL = str
		}
	}

	log.Info().
		Str("cover_ksuid", coverKSUID).
		Str("user_ksuid", result.UserKSUID).
		Str("file_url", result.FileURL).
		Msg("调用存储服务获取封面信息成功")

	return result, nil
}
