package worker

import (
	"context"
	"errors"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"pxpat-backend/internal/storage-cluster/content-processing-service/dto"
	"pxpat-backend/internal/storage-cluster/content-processing-service/processor/video"
	"pxpat-backend/pkg/storage"
	"sync"
	"time"

	"pxpat-backend/internal/storage-cluster/content-processing-service/model"
	"pxpat-backend/internal/storage-cluster/content-processing-service/queue"
	"pxpat-backend/internal/storage-cluster/content-processing-service/repository"
	"pxpat-backend/internal/storage-cluster/content-processing-service/service"
	"pxpat-backend/internal/storage-cluster/content-processing-service/types"
	pkgMessaging "pxpat-backend/pkg/messaging"
)

const (
	taskExpirationTime = 24 * time.Hour // 任务过期时间
)

// Worker 任务处理工作器
type Worker struct {
	config         *types.Config
	queue          *queue.RedisQueue
	storageClient  storage.StorageClient
	processor      *video.FFmpegProcessor
	publisher      *pkgMessaging.Publisher
	taskRepo       repository.VideoProcessingTaskRepository
	mediaResultSvc service.VideoProcessingResultService // 媒体结果服务
	stopChan       chan struct{}
	wg             sync.WaitGroup
	ctx            context.Context
	cancel         context.CancelFunc
}

// NewWorker 创建新的工作器
func NewWorker(cfg *types.Config, queue *queue.RedisQueue, storageClient storage.StorageClient, processor *video.FFmpegProcessor, publisher *pkgMessaging.Publisher, taskRepo repository.VideoProcessingTaskRepository, mediaResultSvc service.VideoProcessingResultService) *Worker {
	ctx, cancel := context.WithCancel(context.Background())
	return &Worker{
		config:         cfg,
		queue:          queue,
		storageClient:  storageClient,
		processor:      processor,
		publisher:      publisher,
		taskRepo:       taskRepo,
		mediaResultSvc: mediaResultSvc,
		stopChan:       make(chan struct{}),
		ctx:            ctx,
		cancel:         cancel,
	}
}

// Start 启动工作器
func (w *Worker) Start(numWorkers int) {
	log.Printf("启动转码,线程: %d ", numWorkers)
	w.wg.Add(numWorkers)

	for i := 0; i < numWorkers; i++ {
		go w.run()
	}

	// 启动清理例程
	w.startCleanupRoutine(1 * time.Hour)
}

// Stop 停止工作器
func (w *Worker) Stop() {
	// 取消上下文，这会中断所有阻塞的Redis操作
	w.cancel()
	close(w.stopChan)
	w.wg.Wait()
	log.Println("All workers stopped")
}

// run 运行工作器主循环
func (w *Worker) run() {
	defer w.wg.Done()
	defer log.Println("Worker received stop signal")

	for {
		select {
		case <-w.stopChan:
			return
		case <-w.ctx.Done():
			return
		default:
			if err := w.processNextTask(w.ctx); err != nil {
				// 如果是上下文取消错误，直接返回
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error processing task: %v", err)
			}
		}
	}
}

// processNextTask 处理下一个任务
func (w *Worker) processNextTask(ctx context.Context) error {
	// 获取任务
	task, err := w.queue.PopTask(ctx, 15*time.Second)
	if err != nil {
		return fmt.Errorf("failed to pop task: %v", err)
	}
	if task == nil {
		return nil // 队列为空
	}

	// 更新任务状态为运行中 (使用PostgreSQL)
	if err := w.taskRepo.UpdateStatus(ctx, task.ID, types.TaskStatusRunning); err != nil {
		return fmt.Errorf("failed to update task status to running: %v", err)
	}

	// 处理视频转码任务
	var processErr error
	switch task.ContentType {
	case model.TaskTypeVideo:
		processErr = w.processor.ProcessVideo(ctx, task)
	}

	// 更新任务状态到PostgreSQL
	if processErr != nil {
		task.Status = types.TaskStatusFailed
		task.Error = processErr.Error()
		log.Printf("Task %d failed: %v", task.ID, processErr)
	} else {
		task.Status = types.TaskStatusCompleted
		log.Printf("Task %d completed successfully", task.ID)
	}

	// 更新任务完成时间
	task.UpdatedAt = time.Now()
	now := time.Now()
	task.CompletedAt = &now

	// 保存完整的任务对象到PostgreSQL（包括元数据）
	if err := w.taskRepo.Update(ctx, task); err != nil {
		return fmt.Errorf("failed to save complete task to PostgreSQL: %v", err)
	}

	// 如果处理成功，将结果数据写入新的媒体结果表
	if processErr == nil {
		if err := w.saveMediaResult(ctx, task); err != nil {
			log.Printf("Warning: failed to save media result for task %d: %v", task.ID, err)
			// 不返回错误，继续发送MQ通知
		}
	}

	// 发送MQ通知
	var eventSent bool
	if processErr != nil {
		eventSent = w.sendTranscodingEvent(ctx, task, "failed", processErr.Error())
	} else {
		eventSent = w.sendTranscodingEvent(ctx, task, "completed", "")
	}

	// 如果发送mq通知失败
	if !eventSent {
		return fmt.Errorf("failed to send transcoding event for task %d", task.ID)
	}

	// 无论成功或失败都清理
	if err := w.processor.Cleanup(task); err != nil {
		log.Printf("Warning: failed to cleanup files for task %d: %v", task.ID, err)
	}

	return nil
}

// startCleanupRoutine 启动清理例程
func (w *Worker) startCleanupRoutine(interval time.Duration) {
	w.wg.Add(1)
	go func() {
		defer w.wg.Done()
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-w.stopChan:
				return
			case <-w.ctx.Done():
				return
			case <-ticker.C:
				if err := w.cleanupExpiredTasks(); err != nil {
					log.Printf("Error during cleanup: %v", err)
				}
			}
		}
	}()
}

// cleanupExpiredTasks 清理过期的任务和文件
func (w *Worker) cleanupExpiredTasks() error {
	// 清理临时目录中的过期文件
	if err := w.cleanupExpiredFiles(w.config.Storage.TempDir); err != nil {
		log.Printf("Error cleaning up temp directory: %v", err)
	}

	// 清理输出目录中的过期文件
	if err := w.cleanupExpiredFiles(w.config.Storage.OutputDir); err != nil {
		log.Printf("Error cleaning up output directory: %v", err)
	}

	return nil
}

// cleanupExpiredFiles 清理指定目录中的过期文件
func (w *Worker) cleanupExpiredFiles(dir string) error {
	now := time.Now()
	entries, err := os.ReadDir(dir)
	if err != nil {
		return fmt.Errorf("failed to read directory: %v", err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		path := filepath.Join(dir, entry.Name())
		info, err := entry.Info()
		if err != nil {
			log.Printf("Warning: failed to get file info for %s: %v", path, err)
			continue
		}

		// 如果文件超过24小时，删除它
		if now.Sub(info.ModTime()) > taskExpirationTime {
			if err := os.Remove(path); err != nil {
				log.Printf("Warning: failed to remove expired file %s: %v", path, err)
			} else {
				log.Printf("Removed expired file: %s", path)
			}
		}
	}

	return nil
}

// sendTranscodingEvent 发送转码完成事件到MQ，返回是否发送成功
func (w *Worker) sendTranscodingEvent(ctx context.Context, task *model.VideoProcessingTask, status, errorMsg string) bool {
	// 如果没有配置MQ或MQ未启用，则跳过
	if w.publisher == nil || !w.publisher.IsConnected() {
		log.Printf("MQ publisher not available, skipping event for task %d", task.ID)
		return false
	}

	// 构建事件
	event := &dto.TranscodingOKEvent{
		TaskID:           task.ID,
		MediaResultID:    task.MediaResultID,
		FileKSUID:        task.FileKSUID,
		ContentKSUID:     task.ContentKSUID,
		Orientation:      task.Orientation,
		Duration:         task.Duration,
		IsAuditTransCode: task.IsAuditTransCode,
		AutoPushAudit:    task.AutoPushAudit,
		AutoPublish:      task.AutoPublish,
		Status:           status,
		Error:            errorMsg,
	}

	// 如果转码成功，添加媒体处理结果数据（直接从task获取）
	if status == "completed" {
		event.Plays = task.Plays
		event.KeyFrames = task.KeyFrames
		event.PreviewVideo = task.PreviewVideo
		log.Printf("Added media processing result data to event - Plays: %s, KeyFrames: %s, PreviewUrl: %s",
			event.Plays, event.KeyFrames, event.PreviewVideo)
	}

	// 发送事件 - 使用pkg messaging的PublishJSON方法，发送到媒体处理服务自己的交换机
	routingKey := pkgMessaging.MEDIA_TRANSCODING_COMPLETED_ROUTING_KEY
	if status == "failed" {
		routingKey = pkgMessaging.MEDIA_TRANSCODING_FAILED_ROUTING_KEY
	}

	if err := w.publisher.PublishJSON(ctx, pkgMessaging.MEDIA_PROCESSING_EXCHANGE, routingKey, event); err != nil {
		log.Printf("Failed to publish transcoding event for task %d: %v", task.ID, err)
		return false
	} else {
		log.Printf("Successfully published transcoding event for task %d with status %s to exchange %s", task.ID, status, pkgMessaging.MEDIA_PROCESSING_EXCHANGE)
		return true
	}
}

// saveMediaResult 将处理结果保存到媒体结果表
func (w *Worker) saveMediaResult(ctx context.Context, task *model.VideoProcessingTask) error {
	log.Printf("Saving media result for task %d", task.ID)

	// 检查是否已有记录
	existingResult, err := w.mediaResultSvc.GetByContentKSUID(ctx, task.ContentKSUID)
	if err != nil && err.Error() != "record not found" {
		return fmt.Errorf("failed to check existing media result: %w", err)
	}

	if existingResult != nil {
		// 更新已有记录
		log.Printf("Updating existing media result: ID=%d, ContentKSUID=%s", existingResult.ID, existingResult.ContentKSUID)

		// 更新字段
		existingResult.Plays = task.Plays
		existingResult.KeyFrames = task.KeyFrames
		existingResult.PreviewVideo = task.PreviewVideo
		existingResult.StorageStatus = task.StorageStatus
		existingResult.Duration = task.Duration
		existingResult.FileSize = task.FileSize
		existingResult.Orientation = task.Orientation

		// 保存更新
		if err := w.mediaResultSvc.Update(ctx, existingResult); err != nil {
			return fmt.Errorf("failed to update existing media result: %w", err)
		}

		log.Printf("Successfully updated media result: ID=%d, ContentKSUID=%s, ContentKSUID=%s",
			existingResult.ID, existingResult.ContentKSUID, existingResult.FileKSUID)
	} else {
		// 创建新记录
		result, err := w.mediaResultSvc.CreateFromTask(ctx, task)
		if err != nil {
			return fmt.Errorf("failed to create media result from task: %w", err)
		}

		log.Printf("Successfully created media result: ID=%d, ContentKSUID=%s, ContentKSUID=%s",
			result.ID, result.ContentKSUID, result.FileKSUID)
	}

	return nil
}
