# Consul 服务发现配置
version: '3.8'

services:
  # Consul 服务发现
  consul:
    image: consul:1.16
    container_name: consul
    restart: unless-stopped
    command: >
      agent -server -bootstrap-expect=1 -ui -client=0.0.0.0 -bind=0.0.0.0
      -config-file=/consul/config/consul.json
    environment:
      - CONSUL_DATACENTER=${CONSUL_DATACENTER:-dc1}
      - CONSUL_ENCRYPT=${CONSUL_ENCRYPT:-your-consul-encrypt-key}
      - CONSUL_CLIENT_ADDR=0.0.0.0
      - CONSUL_BIND_ADDR=0.0.0.0
    volumes:
      - consul_data:/consul/data
      - ../../configs/consul/consul.json:/consul/config/consul.json:ro
    ports:
      - "${CONSUL_HTTP_PORT:-8500}:8500"
      - "${CONSUL_DNS_PORT:-8600}:8600/udp"
      - "${CONSUL_SERF_LAN_PORT:-8301}:8301"
      - "${CONSUL_SERF_WAN_PORT:-8302}:8302"
      - "${CONSUL_SERVER_RPC_PORT:-8300}:8300"
    networks:
      - service-discovery-network
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 3s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# 网络配置
networks:
  service-discovery-network:
    driver: bridge
    external: true

# 数据卷配置
volumes:
  consul_data:
    driver: local
