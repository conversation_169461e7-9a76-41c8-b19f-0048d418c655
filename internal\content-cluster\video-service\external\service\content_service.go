package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/video-service/client"
	"pxpat-backend/internal/content-cluster/video-service/dto"
	"pxpat-backend/internal/content-cluster/video-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/video-service/model"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	"pxpat-backend/internal/content-cluster/video-service/types"
	"pxpat-backend/internal/content-cluster/video-service/utils"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/storage"
)

type ContentService struct {
	contentRepo              *repository.ContentRepository
	categoryRepo             *repository.CategoryRepository
	contentUserRoleRepo      repository.ContentUserRoleRepository
	mqPublisher              *publisher.Publisher
	urlGenerator             *utils.URLGenerator
	storageServiceClient     client.ContentStorageServiceClient
	userServiceClient        client.UserServiceClient
	interactionServiceClient client.InteractionServiceClient
}

func NewContentService(
	contentRepo *repository.ContentRepository,
	categoryRepo *repository.CategoryRepository,
	contentUserRoleRepo repository.ContentUserRoleRepository,
	mqPublisher *publisher.Publisher,
	storageClient storage.StorageClient,
	urlExpiry time.Duration,
	storageServiceConfig client.ContentStorageServiceConfig,
	userServiceClient client.UserServiceClient,
	interactionServiceClient client.InteractionServiceClient,
) *ContentService {
	// 创建存储服务客户端
	storageServiceClient := client.NewStorageServiceClient(storageServiceConfig)

	return &ContentService{
		contentRepo:              contentRepo,
		categoryRepo:             categoryRepo,
		contentUserRoleRepo:      contentUserRoleRepo,
		mqPublisher:              mqPublisher,
		urlGenerator:             utils.NewURLGenerator(storageClient, urlExpiry),
		storageServiceClient:     storageServiceClient,
		userServiceClient:        userServiceClient,
		interactionServiceClient: interactionServiceClient,
	}
}

func (s *ContentService) PublishVideo(ctx context.Context, UserKSUID string, PublishVideoInfo *dto.PublishVideoRequest) (*model.Content, *errors.Errors) {
	log.Info().
		Str("user_ksuid", UserKSUID).
		Str("video_id", PublishVideoInfo.VideoID).
		Str("title", PublishVideoInfo.Title).
		Uint("category", PublishVideoInfo.Category).
		Msg("开始发布视频")

	// 如果是原创视频,则验证协作者
	if gErr := s.validateCollaborators(ctx, UserKSUID, PublishVideoInfo.Collaborators, PublishVideoInfo.SourceType); gErr != nil {
		return nil, gErr
	}

	// 验证分类
	categoryInfo, gErr := s.validateCategory(ctx, UserKSUID, PublishVideoInfo.Category)
	if gErr != nil {
		return nil, gErr
	}

	// 验证VideoID
	if gErr := s.validateVideoID(ctx, UserKSUID, PublishVideoInfo.VideoID); gErr != nil {
		return nil, gErr
	}

	// 验证视频文件
	videoRecord, gErr := s.validateVideoFile(ctx, UserKSUID, PublishVideoInfo.FileKSUID)
	if gErr != nil {
		return nil, gErr
	}

	// 验证封面文件
	coverPath, coverURL, gErr := s.validateCoverFile(ctx, UserKSUID, PublishVideoInfo.CoverKSUID)
	if gErr != nil {
		return nil, gErr
	}

	ContentKSUID := ksuid.GenerateKSUID()
	// 决定是否自动推送审核
	// 如果有协作者，则不自动推送审核，需要等待所有成员同意
	if len(PublishVideoInfo.Collaborators) > 0 &&
		PublishVideoInfo.SourceType == string(model.SourceOriginal) &&
		PublishVideoInfo.AutoPushAudit {
		PublishVideoInfo.AutoPushAudit = false
		log.Info().
			Str("content_ksuid", ContentKSUID).
			Int("collaborator_count", len(PublishVideoInfo.Collaborators)).
			Msg("检测到协作者，禁用自动推送审核，需要等待所有成员同意")
	}

	data := &model.Content{
		ContentKSUID: ContentKSUID,
		Title:        PublishVideoInfo.Title,                               // 标题
		Description:  PublishVideoInfo.Bio,                                 // 描述
		Type:         model.TypeVideo,                                      // 视频类型,视频/动漫/短剧
		Cover:        coverPath,                                            // 设置封面在存储桶中的路径
		CoverURL:     coverURL,                                             // 设置封面公开访问URL
		FileKSUID:    PublishVideoInfo.FileKSUID,                           // 视频文件ID
		VideoID:      PublishVideoInfo.VideoID,                             // 视频编号,类似番号
		PublishAt:    PublishVideoInfo.PublishAt,                           // 计划发布时间
		CreationTime: PublishVideoInfo.CreationTime,                        // 创作时间
		SourceType:   model.ContentSourceType(PublishVideoInfo.SourceType), // 内容来源类型: original(原创)/transshipment(分享)
		CategoryID:   categoryInfo.ID,                                      // 分类ID
		UserKSUID:    UserKSUID,                                            // 设置内容拥有者
		// 原始标签,存储格式: [a,b,c,d]
		// 应当在审核完成后和数据库关联,不存在则创建
		OriginalTags: PublishVideoInfo.OriginalTags,

		HasCollaborators: len(PublishVideoInfo.Collaborators) > 0,
		AllApproved:      len(PublishVideoInfo.Collaborators) == 0, // 如果没有协作者，则默认为已批准
		AutoPushAudit:    PublishVideoInfo.AutoPushAudit,           // 是否自动推送到审核
	}

	log.Info().
		Str("user_ksuid", UserKSUID).
		Str("video_id", PublishVideoInfo.VideoID).
		Str("title", PublishVideoInfo.Title).
		Msg("开始创建内容记录和用户角色关联")

	// 使用事务确保内容创建和用户角色关联要么都成功，要么都失败
	err := s.contentRepo.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var UserIncome float32 = 100.00

		if err := tx.Create(data).Error; err != nil {
			if errors.Is(err, gorm.ErrDuplicatedKey) {
				log.Error().
					Str("user_ksuid", UserKSUID).
					Str("video_id", PublishVideoInfo.VideoID).
					Str("title", PublishVideoInfo.Title).
					Msg("封面或视频文件已被使用")
				return errors.NewGlobalErrors(errors.COVER_OR_VIDEOFILE_USED, errors.COVER_OR_VIDEOFILE_USED, errors.NoneError)
			}
			log.Error().
				Err(err).
				Str("user_ksuid", UserKSUID).
				Str("video_id", PublishVideoInfo.VideoID).
				Str("title", PublishVideoInfo.Title).
				Msg("创建内容记录失败")
			return err
		}

		// 如果有协作者,将已存在账号的协作者添加至collaborators表中,然后从original字段中删除
		if len(PublishVideoInfo.Collaborators) > 0 {
			log.Info().
				Str("content_ksuid", ContentKSUID).
				Int("collaborator_count", len(PublishVideoInfo.Collaborators)).
				Msg("开始处理协作者")

			for processCollaboratorIndex, collaborator := range PublishVideoInfo.Collaborators {
				// 前面会校验原创和分享的协作者
				// 因此这里user_ksuid不存在跳过即可,因为name一定存在
				if collaborator.UserKSUID == "" {
					log.Warn().
						Str("content_ksuid", ContentKSUID).
						Interface("collaborator", collaborator).
						Msg("协作者用户KSUID为空，跳过")
					continue
				}

				// 检查收入分配
				UserIncome = UserIncome - collaborator.Income
				if UserIncome < 0 {
					log.Error().
						Str("content_ksuid", ContentKSUID).
						Str("collaborator_ksuid", collaborator.UserKSUID).
						Str("role", collaborator.RoleName).
						Msg("协作者收入分配错误")
					return errors.NewGlobalErrors(errors.INCOME_ALLOCATION_ERROR, errors.INCOME_ALLOCATION_ERROR, fmt.Errorf("协作者收入分配错误"))
				}

				// 在事务中创建协作者角色关联
				collaboratorRole := &model.Collaborator{
					ContentKSUID: ContentKSUID,
					UserKSUID:    collaborator.UserKSUID,
					RoleName:     collaborator.RoleName,
					RoleOrder:    int8(collaborator.Order),
					Income:       collaborator.Income,
				}

				if err := tx.Create(collaboratorRole).Error; err != nil {
					log.Error().
						Err(err).
						Str("content_ksuid", ContentKSUID).
						Str("collaborator_ksuid", collaborator.UserKSUID).
						Str("role", collaborator.RoleName).
						Msg("创建协作者角色关联失败")
					return err
				}

				// 将用户已存在的协作者从列表中移除
				PublishVideoInfo.Collaborators = append(PublishVideoInfo.Collaborators[:processCollaboratorIndex], PublishVideoInfo.Collaborators[processCollaboratorIndex+1:]...)

				log.Info().
					Str("content_ksuid", ContentKSUID).
					Str("collaborator_ksuid", collaborator.UserKSUID).
					Str("role", collaborator.RoleName).
					Msg("创建协作者角色关联成功")
			}
		}

		// 创建发布者的用户角色
		publisherRole := &model.Collaborator{
			ContentKSUID: ContentKSUID,
			UserKSUID:    UserKSUID,
			RoleName:     "发布者",
			RoleOrder:    0,
			Income:       UserIncome,
			Status:       model.CollaborationStatusAccepted,
			IsUploader:   true,
		}

		if err := tx.Create(publisherRole).Error; err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", ContentKSUID).
				Str("user_ksuid", UserKSUID).
				Msg("创建发布者角色关联失败")
			return err
		}

		log.Info().
			Str("content_ksuid", ContentKSUID).
			Str("user_ksuid", UserKSUID).
			Msg("创建发布者角色关联成功")

		log.Info().
			Str("user_ksuid", UserKSUID).
			Str("content_ksuid", ContentKSUID).
			Str("video_id", PublishVideoInfo.VideoID).
			Str("title", PublishVideoInfo.Title).
			Msg("内容记录和用户角色关联事务成功")

		// 等待将已存在用户协作者处理完后再添加至数据库
		marshaledCollaborators, err := json.Marshal(PublishVideoInfo.Collaborators)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", UserKSUID).
				Str("content_ksuid", ContentKSUID).
				Msg("序列化协作者失败")
			return err
		}
		data.OriginalCollaborators = string(marshaledCollaborators)

		if err := tx.Model(&model.Content{}).
			Where("content_ksuid = ?", ContentKSUID).
			Update("original_collaborators", data.OriginalCollaborators).
			Error; err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", UserKSUID).
				Str("video_id", PublishVideoInfo.VideoID).
				Str("title", PublishVideoInfo.Title).
				Msg("更新内容记录失败")
			return err
		}

		log.Info().
			Str("user_ksuid", UserKSUID).
			Str("content_ksuid", ContentKSUID).
			Str("video_id", PublishVideoInfo.VideoID).
			Str("title", PublishVideoInfo.Title).
			Msg("内容记录创建成功")

		// 发送转码请求到媒体处理服务
		if s.mqPublisher != nil && s.mqPublisher.IsConnected() {
			resolutions := []string{"720p"}
			if err := s.mqPublisher.GetTranscodingPublisher().PublishTranscodingRequest(ctx, data.UserKSUID, ContentKSUID, data.FileKSUID, videoRecord.Ext, true, PublishVideoInfo.AutoPushAudit, resolutions); err != nil {
				log.Error().
					Err(err).
					Str("user_ksuid", UserKSUID).
					Str("content_ksuid", ContentKSUID).
					Msg("发送转码请求失败")
				return err
			}
		} else {
			log.Warn().Msg("MQ发布器未配置或未连接，跳过转码请求")
		}

		return nil
	})

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", UserKSUID).
			Str("video_id", PublishVideoInfo.VideoID).
			Str("title", PublishVideoInfo.Title).
			Msg("视频发布失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.TRANSACTION_ERROR, err)
	}

	log.Info().
		Str("user_ksuid", UserKSUID).
		Str("content_ksuid", ContentKSUID).
		Str("video_id", PublishVideoInfo.VideoID).
		Str("title", PublishVideoInfo.Title).
		Msg("视频发布完成")

	return data, nil
}

func (s *ContentService) GetContentByContentKSUID(ctx context.Context, contentKSUID string) (*dto.ContentDetailWithUserInfoAndCollaborators, *errors.Errors) {
	log.Info().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容详情")

	content, err := s.contentRepo.GetContentByContentKSUID(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容详情失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 处理URL生成逻辑
	if s.urlGenerator != nil {
		if err := s.processContentURLs(ctx, content); err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("处理内容URL失败")
			// 不返回错误，继续返回内容，只是URL可能为空
		}
	}

	// 批量获取用户信息并构建详情DTO
	contentDetail, err := s.enrichContentDetail(ctx, content)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("批量获取用户信息失败")
		// 不返回错误，继续返回内容，只是用户信息可能为空
		contentDetail = &dto.ContentDetailWithUserInfoAndCollaborators{
			ExportContentModel: dto.ContentToExportContentModel(content),
		}
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Str("title", content.Title).
		Str("status", string(content.Status)).
		Int("collaborators_count", len(contentDetail.Collaborators)).
		Msg("获取内容详情成功")

	return contentDetail, nil
}

// processContentURLs 处理内容URL生成逻辑
func (s *ContentService) processContentURLs(ctx context.Context, content *model.Content) error {
	var needsUpdate bool

	// 处理播放URL - 生成JSON格式的URL数据存储到PlayURL字段
	if content.PlayURL == "" || s.urlGenerator.IsPlayURLsExpired(content.PlayURL) {
		if content.Plays != "" {
			playURLsJSON, err := s.urlGenerator.GeneratePlayURLs(ctx, content.Plays)
			if err != nil {
				log.Error().
					Err(err).
					Str("content_ksuid", content.ContentKSUID).
					Str("plays", content.Plays).
					Msg("生成播放URLs失败")
			} else if playURLsJSON != "" && playURLsJSON != content.Plays {
				content.PlayURL = playURLsJSON
				needsUpdate = true
				log.Info().
					Str("content_ksuid", content.ContentKSUID).
					Msg("成功生成播放URLs JSON")
			}
		}
	}

	// 处理关键帧URL - 生成JSON数组格式的URL数据存储到KeyFramesURL字段
	if content.KeyFramesURL == "" || s.urlGenerator.IsKeyFramesURLsExpired(content.KeyFramesURL) {
		if content.KeyFrames != "" {
			keyFramesURLsJSON, err := s.urlGenerator.GenerateKeyFramesURLs(ctx, content.KeyFrames)
			if err != nil {
				log.Error().
					Err(err).
					Str("content_ksuid", content.ContentKSUID).
					Str("key_frames", content.KeyFrames).
					Msg("生成关键帧URLs失败")
			} else if keyFramesURLsJSON != "" && keyFramesURLsJSON != content.KeyFrames {
				content.KeyFramesURL = keyFramesURLsJSON
				needsUpdate = true
				log.Info().
					Str("content_ksuid", content.ContentKSUID).
					Msg("成功生成关键帧URLs JSON")
			}
		}
	}

	// 处理预览视频URL - 生成单个URL字符串存储到PreviewURL字段
	if content.PreviewURL == "" || s.urlGenerator.IsURLExpired(content.PreviewURL) {
		if content.PreviewVideo != "" {
			previewURL, err := s.urlGenerator.GeneratePreviewURL(ctx, content.PreviewVideo)
			if err != nil {
				log.Error().
					Err(err).
					Str("content_ksuid", content.ContentKSUID).
					Str("preview_video", content.PreviewVideo).
					Msg("生成预览视频URL失败")
			} else if previewURL != "" {
				content.PreviewURL = previewURL
				needsUpdate = true
				log.Info().
					Str("content_ksuid", content.ContentKSUID).
					Str("preview_url", previewURL).
					Msg("成功生成预览视频URL")
			}
		}
	}

	// 如果有URL更新，保存到数据库
	if needsUpdate {
		if err := s.contentRepo.UpdateContentURLs(ctx, content.ContentKSUID, content.PlayURL, content.KeyFramesURL, content.PreviewURL); err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", content.ContentKSUID).
				Msg("更新内容URL到数据库失败")
			return err
		}
		log.Info().
			Str("content_ksuid", content.ContentKSUID).
			Msg("成功更新内容URL到数据库")
	}

	return nil
}

// processContentsURLs 批量处理内容列表中的URL过期检查和重新生成
func (s *ContentService) processContentsURLs(ctx context.Context, contents []*model.Content) error {
	var updatedContents []*model.Content

	for _, content := range contents {
		var needsUpdate bool
		originalPlayURL := content.PlayURL
		originalKeyFramesURL := content.KeyFramesURL
		originalPreviewURL := content.PreviewURL

		// 处理播放URL - 生成JSON格式的URL数据存储到PlayURL字段
		if content.PlayURL == "" || s.urlGenerator.IsPlayURLsExpired(content.PlayURL) {
			if content.Plays != "" {
				playURLsJSON, err := s.urlGenerator.GeneratePlayURLs(ctx, content.Plays)
				if err != nil {
					log.Error().
						Err(err).
						Str("content_ksuid", content.ContentKSUID).
						Str("plays", content.Plays).
						Msg("生成播放URLs失败")
				} else if playURLsJSON != "" && playURLsJSON != content.Plays {
					content.PlayURL = playURLsJSON
					needsUpdate = true
					log.Debug().
						Str("content_ksuid", content.ContentKSUID).
						Msg("成功生成播放URLs JSON")
				}
			}
		}

		// 处理关键帧URL - 生成JSON数组格式的URL数据存储到KeyFramesURL字段
		if content.KeyFramesURL == "" || s.urlGenerator.IsKeyFramesURLsExpired(content.KeyFramesURL) {
			if content.KeyFrames != "" {
				keyFramesURLsJSON, err := s.urlGenerator.GenerateKeyFramesURLs(ctx, content.KeyFrames)
				if err != nil {
					log.Error().
						Err(err).
						Str("content_ksuid", content.ContentKSUID).
						Str("key_frames", content.KeyFrames).
						Msg("生成关键帧URLs失败")
				} else if keyFramesURLsJSON != "" && keyFramesURLsJSON != content.KeyFrames {
					content.KeyFramesURL = keyFramesURLsJSON
					needsUpdate = true
					log.Debug().
						Str("content_ksuid", content.ContentKSUID).
						Msg("成功生成关键帧URLs JSON")
				}
			}
		}

		// 处理预览视频URL - 生成单个URL字符串存储到PreviewURL字段
		if content.PreviewURL == "" || s.urlGenerator.IsURLExpired(content.PreviewURL) {
			if content.PreviewVideo != "" {
				previewURL, err := s.urlGenerator.GeneratePreviewURL(ctx, content.PreviewVideo)
				if err != nil {
					log.Error().
						Err(err).
						Str("content_ksuid", content.ContentKSUID).
						Str("preview_video", content.PreviewVideo).
						Msg("生成预览视频URL失败")
				} else if previewURL != "" {
					content.PreviewURL = previewURL
					needsUpdate = true
					log.Debug().
						Str("content_ksuid", content.ContentKSUID).
						Str("preview_url", previewURL).
						Msg("成功生成预览视频URL")
				}
			}
		}

		// 如果有URL更新，记录需要更新的内容
		if needsUpdate {
			updatedContents = append(updatedContents, content)
			log.Debug().
				Str("content_ksuid", content.ContentKSUID).
				Str("original_play_url", originalPlayURL).
				Str("new_play_url", content.PlayURL).
				Str("original_keyframes_url", originalKeyFramesURL).
				Str("new_keyframes_url", content.KeyFramesURL).
				Str("original_preview_url", originalPreviewURL).
				Str("new_preview_url", content.PreviewURL).
				Msg("内容URL需要更新")
		}
	}

	// 批量更新数据库
	if len(updatedContents) > 0 {
		for _, content := range updatedContents {
			if err := s.contentRepo.UpdateContentURLs(ctx, content.ContentKSUID, content.PlayURL, content.KeyFramesURL, content.PreviewURL); err != nil {
				log.Error().
					Err(err).
					Str("content_ksuid", content.ContentKSUID).
					Msg("更新内容URL到数据库失败")
				// 继续处理其他内容，不中断整个流程
			} else {
				log.Debug().
					Str("content_ksuid", content.ContentKSUID).
					Msg("成功更新内容URL到数据库")
			}
		}
		log.Info().
			Int("updated_count", len(updatedContents)).
			Int("total_count", len(contents)).
			Msg("批量更新内容URL完成")
	}

	return nil
}

// GetPublishedContentsByType 根据类型获取已发布的内容，支持分页和排序
func (s *ContentService) GetPublishedContentsByType(ctx context.Context, contentType string, page, pageSize int, sortBy, sourceType, userKSUID string, tagIDs []uint, categoryID uint, level string) ([]*dto.ContentSimpleModel, int64, *errors.Errors) {
	log.Info().
		Str("content_type", contentType).
		Int("page", page).
		Int("page_size", pageSize).
		Str("sort_by", sortBy).
		Str("source_type", sourceType).
		Str("user_ksuid", userKSUID).
		Interface("tag_ids", tagIDs).
		Uint("category_id", categoryID).
		Str("level", level).
		Msg("开始获取指定类型的已发布内容列表")

	// 参数验证
	if contentType == "" {
		log.Warn().
			Str("content_type", contentType).
			Msg("内容类型参数为空")
		return nil, 0, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, errors.NoneError)
	}

	// 验证排序参数是否有效
	validSortTypes := map[string]bool{
		"comprehensive": true,
		"views":         true,
		"latest":        true,
		"favorites":     true,
		"likes":         true,
		"shares":        true,
	}

	if !validSortTypes[sortBy] {
		log.Warn().
			Str("sort_by", sortBy).
			Msg("无效的排序参数，使用默认排序")
		sortBy = "latest"
	}

	// 调用repository层获取数据
	contents, total, err := s.contentRepo.GetPublishedContentsByType(ctx, contentType, page, pageSize, sortBy, sourceType, userKSUID, tagIDs, categoryID, level)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Int("page", page).
			Int("page_size", pageSize).
			Str("sort_by", sortBy).
			Str("source_type", sourceType).
			Str("user_ksuid", userKSUID).
			Interface("tag_ids", tagIDs).
			Uint("category_id", categoryID).
			Str("level", level).
			Msg("获取指定类型已发布内容列表失败")
		return nil, 0, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 处理内容列表中的URL过期检查和重新生成
	if s.urlGenerator != nil && len(contents) > 0 {
		if err := s.processContentsURLs(ctx, contents); err != nil {
			log.Error().
				Err(err).
				Str("content_type", contentType).
				Int("count", len(contents)).
				Msg("处理内容列表URL失败")
			// 不返回错误，继续返回内容，只是URL可能为空
		}
	}

	// 批量获取用户信息并转换为ContentSimpleModel
	contentSimpleModels, err := s.enrichContentsSimple(ctx, contents)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Int("count", len(contents)).
			Msg("批量获取用户信息失败")
		// 不返回错误，继续返回内容，只是用户信息可能为空
	}

	log.Info().
		Str("content_type", contentType).
		Int("page", page).
		Int("page_size", pageSize).
		Str("sort_by", sortBy).
		Int64("total", total).
		Int("count", len(contentSimpleModels)).
		Msg("获取指定类型已发布内容列表成功（包含用户信息）")

	return contentSimpleModels, total, nil
}

// GetMyContents 获取用户自己的投稿内容，按时间倒序，支持分页和内容类型筛选
func (s *ContentService) GetMyContents(ctx context.Context, userKSUID string, page, pageSize int, contentType string) ([]*dto.ContentSimpleModel, int64, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("content_type", contentType).
		Msg("开始获取用户投稿内容列表")

	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20 // 默认每页20条
	}
	if pageSize > 100 {
		pageSize = 100 // 最大每页100条
	}

	// 调用repository层获取数据
	contents, total, err := s.contentRepo.GetMyContents(ctx, userKSUID, page, pageSize, contentType)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("content_type", contentType).
			Msg("获取用户投稿内容列表失败")
		return nil, 0, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	csms := dto.ConvertToContentSimpleModels(contents)

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("content_type", contentType).
		Int64("total", total).
		Int("count", len(contents)).
		Msg("获取用户投稿内容列表成功")

	return csms, total, nil
}

// GetRandomVideos 随机获取视频，支持按标签和分类过滤
func (s *ContentService) GetRandomVideos(ctx context.Context, tagID, categoryID uint, level, orientation string, size int) ([]*dto.ContentSimpleModel, *errors.Errors) {
	log.Info().
		Uint("tag_id", tagID).
		Uint("category_id", categoryID).
		Str("level", level).
		Str("orientation", orientation).
		Int("size", size).
		Msg("开始随机获取视频")

	// 如果指定了标签ID，验证标签是否存在
	if tagID > 0 {
		var tag model.Tag
		err := s.contentRepo.GetDB().WithContext(ctx).
			Where("id = ?", tagID).
			First(&tag).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Warn().
					Uint("tag_id", tagID).
					Msg("指定的标签ID不存在")
				return nil, errors.NewGlobalErrors(errors.GET_TAG_NOT_EXISTS_ERROR, errors.GET_TAG_NOT_EXISTS_ERROR, err)
			}
			log.Error().
				Err(err).
				Uint("tag_id", tagID).
				Msg("验证标签ID时失败")
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
	}

	// 如果指定了分类ID，验证分类是否存在
	if categoryID > 0 {
		_, err := s.categoryRepo.GetCategoryByID(ctx, categoryID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Warn().
					Uint("category_id", categoryID).
					Msg("指定的分类ID不存在")
				return nil, errors.NewGlobalErrors(errors.NOT_VALID_CATEGORY, errors.NOT_VALID_CATEGORY, err)
			}
			log.Error().
				Err(err).
				Uint("category_id", categoryID).
				Msg("验证分类ID时失败")
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
	}

	// 调用repository层获取随机视频
	contents, err := s.contentRepo.GetRandomVideos(ctx, tagID, categoryID, level, orientation, size)
	if err != nil {
		log.Error().
			Err(err).
			Uint("tag_id", tagID).
			Uint("category_id", categoryID).
			Str("level", level).
			Str("orientation", orientation).
			Int("size", size).
			Msg("随机获取视频失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 处理内容列表中的URL过期检查和重新生成
	if s.urlGenerator != nil && len(contents) > 0 {
		if err := s.processContentsURLs(ctx, contents); err != nil {
			log.Error().
				Err(err).
				Uint("tag_id", tagID).
				Uint("category_id", categoryID).
				Str("orientation", orientation).
				Int("count", len(contents)).
				Msg("处理随机视频列表URL失败")
			// 不返回错误，继续返回内容，只是URL可能为空
		}
	}

	// 批量获取用户信息并转换为包含用户信息的DTO
	contentSimpleModels, err := s.enrichContentsSimple(ctx, contents)
	if err != nil {
		log.Error().
			Err(err).
			Uint("tag_id", tagID).
			Uint("category_id", categoryID).
			Int("count", len(contents)).
			Msg("批量获取用户信息失败")
		// 不返回错误，继续返回内容，只是用户信息可能为空
	}

	log.Info().
		Uint("tag_id", tagID).
		Uint("category_id", categoryID).
		Str("level", level).
		Str("orientation", orientation).
		Int("size", size).
		Int("count", len(contentSimpleModels)).
		Msg("随机获取视频成功（包含用户信息）")

	return contentSimpleModels, nil
}

// GetUserPublishedVideos 获取指定用户发布的视频，支持分页和排序
func (s *ContentService) GetUserPublishedVideos(ctx context.Context, userKSUID string, page, pageSize int, sortBy, contentType string) ([]*dto.ContentSimpleModel, int64, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("sort_by", sortBy).
		Str("content_type", contentType).
		Msg("开始获取指定用户发布的视频列表")

	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20 // 默认每页20条
	}
	if pageSize > 100 {
		pageSize = 100 // 最大每页100条
	}

	// 调用repository层获取数据
	contents, total, err := s.contentRepo.GetUserPublishedVideos(ctx, userKSUID, page, pageSize, sortBy, contentType)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("sort_by", sortBy).
			Str("content_type", contentType).
			Msg("获取指定用户发布视频列表失败")
		return nil, 0, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 处理内容列表中的URL过期检查和重新生成
	if s.urlGenerator != nil && len(contents) > 0 {
		if err := s.processContentsURLs(ctx, contents); err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", userKSUID).
				Int("count", len(contents)).
				Msg("处理用户发布视频列表URL失败")
			// 不返回错误，继续返回内容，只是URL可能为空
		}
	}

	// 批量获取用户信息并转换为ContentSimpleModel
	contentSimpleModels, err := s.enrichContentsSimple(ctx, contents)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("count", len(contents)).
			Msg("批量获取用户信息失败")
		// 不返回错误，继续返回内容，只是用户信息可能为空
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("sort_by", sortBy).
		Str("content_type", contentType).
		Int64("total", total).
		Int("count", len(contentSimpleModels)).
		Msg("获取指定用户发布视频列表成功（包含用户信息）")

	return contentSimpleModels, total, nil
}

func (s *ContentService) validateCollaborators(ctx context.Context, userKSUID string, collaborators []dto.CollaboratorBaseInfo, sourceType string) *errors.Errors {
	// 如果有协作者,开始校验
	if len(collaborators) > 0 {
		// 声明要验证的协作者KSUID列表
		collaboratorsKSUID := make([]string, 0, len(collaborators))

		// 必要验证,role_name, income, order 是否存在
		for _, collaborator := range collaborators {
			if collaborator.RoleName == "" ||
				collaborator.Income < 0 ||
				collaborator.Income > 100 ||
				collaborator.Order < 0 {
				// 如果必要参数不全,直接报错
				return errors.NewGlobalErrors(errors.INVALID_COLLABORATOR_PARAMETER, errors.INVALID_COLLABORATOR_PARAMETER, errors.NoneError)
			}

			if collaborator.UserKSUID == "" {
				// 如果user_ksuid为空
				if sourceType == string(model.SourceOriginal) {
					// 且是原创,直接报错
					return errors.NewGlobalErrors(errors.INVALID_COLLABORATOR_PARAMETER, errors.INVALID_COLLABORATOR_PARAMETER, errors.NoneError)
				} else {
					// 如果是分享,判断 name 是否存在
					if collaborator.Name == "" {
						// 如果分享连user_ksuid和name都为空,则报错
						return errors.NewGlobalErrors(errors.INVALID_COLLABORATOR_PARAMETER, errors.INVALID_COLLABORATOR_PARAMETER, errors.NoneError)
					}
				}
			} else {
				// 如果user_ksuid不为空,忽略name和source_type验证,直接加入验证用户是否存在列表
				collaboratorsKSUID = append(collaboratorsKSUID, collaborator.UserKSUID)
			}
		}

		log.Info().
			Str("user_ksuid", userKSUID).
			Strs("collaborators_ksuid", collaboratorsKSUID).
			Msg("开始验证协作者是否存在")

		users, err := s.userServiceClient.BatchGetUsers(collaboratorsKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", userKSUID).
				Strs("collaborators_ksuid", collaboratorsKSUID).
				Msg("调用用户服务验证协作者失败")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.SEND_INTERNAL_HTTP_ERROR, err)
		}

		log.Info().
			Interface("users", users).
			Msg("调用用户服务验证协作者成功")

		if users.Total != len(collaboratorsKSUID) {
			log.Warn().
				Str("user_ksuid", userKSUID).
				Strs("collaborators_ksuid", collaboratorsKSUID).
				Int("expected_count", len(collaboratorsKSUID)).
				Int("actual_count", users.Total).
				Msg("协作者验证失败：部分协作者不存在")
			return errors.NewGlobalErrors(errors.COLLABORATOR_NOT_FOUND, errors.COLLABORATOR_NOT_FOUND, fmt.Errorf("协作者不存在"))
		}

		log.Info().
			Str("user_ksuid", userKSUID).
			Int("collaborator_count", len(collaboratorsKSUID)).
			Msg("协作者验证成功")

	}
	// 如果是原创视频,必须检查协作者是否存在

	return nil
}

// validateCategory 验证分类是否为子级分类
func (s *ContentService) validateCategory(ctx context.Context, userKSUID string, categoryID uint) (*model.Category, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Msg("开始验证分类")

	categoryInfo, err := s.categoryRepo.GetCategoryByID(ctx, categoryID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Uint("category", categoryID).
			Msg("获取分类信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if categoryInfo.Level != 1 {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Uint("category", categoryID).
			Int("level", categoryInfo.Level).
			Msg("分类不是子级分类")
		return nil, errors.NewGlobalErrors(errors.NOT_VALID_CATEGORY, errors.NOT_VALID_CATEGORY, errors.NoneError)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Str("category_name", categoryInfo.Name).
		Msg("分类验证成功")

	return categoryInfo, nil
}

// validateAnimeCategory 验证动漫分类
func (s *ContentService) validateAnimeCategory(ctx context.Context, userKSUID string, categoryID uint) (*model.Category, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Msg("开始验证动漫分类")

	// 首先进行基础分类验证
	categoryInfo, gErr := s.validateCategory(ctx, userKSUID, categoryID)
	if gErr != nil {
		return nil, gErr
	}

	// 验证分类的VideoType必须为anime
	if categoryInfo.VideoType != "anime" {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Uint("category_id", categoryID).
			Str("video_type", categoryInfo.VideoType).
			Msg("分类不是动漫分类")
		return nil, errors.NewGlobalErrors(errors.NOT_ANIME_CATEGORY, errors.NOT_ANIME_CATEGORY, errors.NoneError)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Uint("category_id", categoryID).
		Str("category_name", categoryInfo.Name).
		Str("video_type", categoryInfo.VideoType).
		Msg("动漫分类验证成功")

	return categoryInfo, nil
}

// PublishAnime 发布动漫
func (s *ContentService) PublishAnime(ctx context.Context, UserKSUID string, PublishAnimeInfo *dto.PublishAnimeRequest) (*model.Content, *errors.Errors) {
	log.Info().
		Str("user_ksuid", UserKSUID).
		Str("video_id", PublishAnimeInfo.VideoID).
		Str("title", PublishAnimeInfo.Title).
		Uint("category", PublishAnimeInfo.Category).
		Msg("开始发布动漫")

	// 如果是原创动漫,则验证协作者
	if gErr := s.validateCollaborators(ctx, UserKSUID, PublishAnimeInfo.Collaborators, PublishAnimeInfo.SourceType); gErr != nil {
		return nil, gErr
	}

	// 验证动漫分类（包含动漫类型验证）
	categoryInfo, gErr := s.validateAnimeCategory(ctx, UserKSUID, PublishAnimeInfo.Category)
	if gErr != nil {
		return nil, gErr
	}

	// 验证VideoID
	if gErr := s.validateVideoID(ctx, UserKSUID, PublishAnimeInfo.VideoID); gErr != nil {
		return nil, gErr
	}

	// 验证视频文件
	videoRecord, gErr := s.validateVideoFile(ctx, UserKSUID, PublishAnimeInfo.FileKSUID)
	if gErr != nil {
		return nil, gErr
	}

	// 验证封面文件
	coverPath, coverURL, gErr := s.validateCoverFile(ctx, UserKSUID, PublishAnimeInfo.CoverKSUID)
	if gErr != nil {
		return nil, gErr
	}

	ContentKSUID := ksuid.GenerateKSUID()
	// 决定是否自动推送审核
	// 如果有协作者，则不自动推送审核，需要等待所有成员同意
	if len(PublishAnimeInfo.Collaborators) > 0 &&
		PublishAnimeInfo.SourceType == string(model.SourceOriginal) &&
		PublishAnimeInfo.AutoPushAudit {
		PublishAnimeInfo.AutoPushAudit = false
		log.Info().
			Str("content_ksuid", ContentKSUID).
			Int("collaborator_count", len(PublishAnimeInfo.Collaborators)).
			Msg("检测到协作者，禁用自动推送审核，需要等待所有成员同意")
	}

	// 创建内容记录
	data := &model.Content{
		ContentKSUID: ContentKSUID,
		Title:        PublishAnimeInfo.Title,
		Description:  PublishAnimeInfo.Bio,
		Type:         model.TypeAnime, // 设置为动漫类型
		VideoID:      PublishAnimeInfo.VideoID,
		FileKSUID:    PublishAnimeInfo.FileKSUID,
		Cover:        coverPath,
		CoverURL:     coverURL,
		Status:       model.StatusDraft,
		CreationTime: PublishAnimeInfo.CreationTime,
		SourceType:   model.ContentSourceType(PublishAnimeInfo.SourceType), // 内容来源类型: original(原创)/transshipment(分享)
		CategoryID:   categoryInfo.ID,                                      // 分类ID
		UserKSUID:    UserKSUID,                                            // 设置内容拥有者
		// 原始标签,存储格式: [a,b,c,d]
		// 应当在审核完成后和数据库关联,不存在则创建
		OriginalTags: PublishAnimeInfo.OriginalTags,

		HasCollaborators: len(PublishAnimeInfo.Collaborators) > 0,
		AllApproved:      len(PublishAnimeInfo.Collaborators) == 0, // 如果没有协作者，则默认为已批准
		AutoPushAudit:    PublishAnimeInfo.AutoPushAudit,           // 是否自动推送到审核
	}

	log.Info().
		Str("user_ksuid", UserKSUID).
		Str("video_id", PublishAnimeInfo.VideoID).
		Str("title", PublishAnimeInfo.Title).
		Msg("开始创建动漫内容记录和用户角色关联")

	// 使用事务确保内容创建和用户角色关联要么都成功，要么都失败
	err := s.contentRepo.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var UserIncome float32 = 100.00

		if err := tx.Create(data).Error; err != nil {
			if errors.Is(err, gorm.ErrDuplicatedKey) {
				log.Error().
					Str("user_ksuid", UserKSUID).
					Str("video_id", PublishAnimeInfo.VideoID).
					Str("title", PublishAnimeInfo.Title).
					Msg("封面或视频文件已被使用")
				return errors.NewGlobalErrors(errors.COVER_OR_VIDEOFILE_USED, errors.COVER_OR_VIDEOFILE_USED, errors.NoneError)
			}
			log.Error().
				Err(err).
				Str("user_ksuid", UserKSUID).
				Str("video_id", PublishAnimeInfo.VideoID).
				Str("title", PublishAnimeInfo.Title).
				Msg("创建动漫内容记录失败")
			return err
		}

		// 如果有协作者,将已存在账号的协作者添加至collaborators表中,然后从original字段中删除
		if len(PublishAnimeInfo.Collaborators) > 0 {
			log.Info().
				Str("content_ksuid", ContentKSUID).
				Int("collaborator_count", len(PublishAnimeInfo.Collaborators)).
				Msg("开始处理动漫协作者")

			for processCollaboratorIndex, collaborator := range PublishAnimeInfo.Collaborators {
				// 前面会校验原创和分享的协作者
				// 因此这里user_ksuid不存在跳过即可,因为name一定存在
				if collaborator.UserKSUID == "" {
					log.Warn().
						Str("content_ksuid", ContentKSUID).
						Interface("collaborator", collaborator).
						Msg("动漫协作者用户KSUID为空，跳过")
					continue
				}

				// 检查收入分配
				UserIncome = UserIncome - collaborator.Income
				if UserIncome < 0 {
					log.Error().
						Str("content_ksuid", ContentKSUID).
						Str("collaborator_ksuid", collaborator.UserKSUID).
						Str("role", collaborator.RoleName).
						Msg("动漫协作者收入分配错误")
					return errors.NewGlobalErrors(errors.INCOME_ALLOCATION_ERROR, errors.INCOME_ALLOCATION_ERROR, fmt.Errorf("协作者收入分配错误"))
				}

				// 在事务中创建协作者角色关联
				collaboratorRole := &model.Collaborator{
					ContentKSUID: ContentKSUID,
					UserKSUID:    collaborator.UserKSUID,
					RoleName:     collaborator.RoleName,
					RoleOrder:    int8(collaborator.Order),
					Income:       collaborator.Income,
				}

				if err := tx.Create(collaboratorRole).Error; err != nil {
					log.Error().
						Err(err).
						Str("content_ksuid", ContentKSUID).
						Str("collaborator_ksuid", collaborator.UserKSUID).
						Str("role", collaborator.RoleName).
						Msg("创建动漫协作者角色关联失败")
					return err
				}

				// 将用户已存在的协作者从列表中移除
				PublishAnimeInfo.Collaborators = append(PublishAnimeInfo.Collaborators[:processCollaboratorIndex], PublishAnimeInfo.Collaborators[processCollaboratorIndex+1:]...)

				log.Info().
					Str("content_ksuid", ContentKSUID).
					Str("collaborator_ksuid", collaborator.UserKSUID).
					Str("role", collaborator.RoleName).
					Msg("创建动漫协作者角色关联成功")
			}
		}

		// 创建发布者的用户角色
		publisherRole := &model.Collaborator{
			ContentKSUID: ContentKSUID,
			UserKSUID:    UserKSUID,
			RoleName:     "发布者",
			RoleOrder:    0,
			Income:       UserIncome,
			Status:       model.CollaborationStatusAccepted,
			IsUploader:   true,
		}

		if err := tx.Create(publisherRole).Error; err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", ContentKSUID).
				Str("user_ksuid", UserKSUID).
				Msg("创建动漫发布者角色关联失败")
			return err
		}

		log.Info().
			Str("content_ksuid", ContentKSUID).
			Str("user_ksuid", UserKSUID).
			Msg("创建动漫发布者角色关联成功")

		log.Info().
			Str("user_ksuid", UserKSUID).
			Str("content_ksuid", ContentKSUID).
			Str("video_id", PublishAnimeInfo.VideoID).
			Str("title", PublishAnimeInfo.Title).
			Msg("动漫内容记录和用户角色关联事务成功")

		// 等待将已存在用户协作者处理完后再添加至数据库
		marshaledCollaborators, err := json.Marshal(PublishAnimeInfo.Collaborators)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", UserKSUID).
				Str("content_ksuid", ContentKSUID).
				Msg("序列化动漫协作者失败")
			return err
		}
		data.OriginalCollaborators = string(marshaledCollaborators)

		if err := tx.Model(&model.Content{}).
			Where("content_ksuid = ?", ContentKSUID).
			Update("original_collaborators", data.OriginalCollaborators).
			Error; err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", UserKSUID).
				Str("video_id", PublishAnimeInfo.VideoID).
				Str("title", PublishAnimeInfo.Title).
				Msg("更新动漫内容记录失败")
			return err
		}

		log.Info().
			Str("user_ksuid", UserKSUID).
			Str("content_ksuid", ContentKSUID).
			Str("video_id", PublishAnimeInfo.VideoID).
			Str("title", PublishAnimeInfo.Title).
			Msg("动漫内容记录创建成功")

		// 发送转码请求到媒体处理服务
		if s.mqPublisher != nil && s.mqPublisher.IsConnected() {
			resolutions := []string{"720p"}
			if err := s.mqPublisher.GetTranscodingPublisher().PublishTranscodingRequest(ctx, data.UserKSUID, ContentKSUID, data.FileKSUID, videoRecord.Ext, true, PublishAnimeInfo.AutoPushAudit, resolutions); err != nil {
				log.Error().
					Err(err).
					Str("user_ksuid", UserKSUID).
					Str("content_ksuid", ContentKSUID).
					Msg("发送动漫转码请求失败")
				return err
			}
		} else {
			log.Warn().Msg("MQ发布器未配置或未连接，跳过动漫转码请求")
		}

		return nil
	})

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", UserKSUID).
			Str("video_id", PublishAnimeInfo.VideoID).
			Str("title", PublishAnimeInfo.Title).
			Msg("动漫发布失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.TRANSACTION_ERROR, err)
	}

	log.Info().
		Str("user_ksuid", UserKSUID).
		Str("content_ksuid", ContentKSUID).
		Str("video_id", PublishAnimeInfo.VideoID).
		Str("title", PublishAnimeInfo.Title).
		Msg("动漫发布成功")

	return data, nil
}

// validateVideoID 验证VideoID是否已存在
func (s *ContentService) validateVideoID(ctx context.Context, userKSUID string, videoID string) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("video_id", videoID).
		Msg("开始验证VideoID")

	exist, err := s.contentRepo.VideoIDExist(ctx, videoID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("video_id", videoID).
			Msg("检查VideoID是否存在时发生错误")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if exist {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("video_id", videoID).
			Msg("VideoID已存在")
		return errors.NewGlobalErrors(errors.VIDEO_ID_EXISTS, errors.VIDEO_ID_EXISTS, errors.NoneError)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("video_id", videoID).
		Msg("VideoID验证成功")

	return nil
}

// validateVideoFile 验证视频文件信息
func (s *ContentService) validateVideoFile(ctx context.Context, userKSUID string, fileKSUID string) (*types.VideoFileInfo, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("file_ksuid", fileKSUID).
		Msg("开始验证视频文件信息")

	GResponse, err := s.storageServiceClient.GetVideoInfo(fileKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("file_ksuid", fileKSUID).
			Msg("调用存储服务获取文件信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.SEND_INTERNAL_HTTP_ERROR, err)
	}

	if GResponse.Code != 1 {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("file_ksuid", fileKSUID).
			Int("response_code", GResponse.Code).
			Interface("data", GResponse.Data).
			Msg("存储服务返回错误")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.INTERNAL_HTTP_RESPONSE_ERROR, fmt.Errorf(GResponse.Message))
	}

	var videoRecord types.VideoFileInfo
	jsonBytes, err := json.Marshal(GResponse.Data)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("file_ksuid", fileKSUID).
			Msg("序列化文件信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.JSON_MARSHAL_ERROR, err)
	}

	err = json.Unmarshal(jsonBytes, &videoRecord)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("file_ksuid", fileKSUID).
			Msg("反序列化视频文件信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.JSON_UNMARSHAL_ERROR, err)
	}

	if videoRecord.UserKSUID != userKSUID {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("file_ksuid", fileKSUID).
			Str("video_owner", videoRecord.UserKSUID).
			Msg("视频文件所有者不匹配")
		return nil, errors.NewGlobalErrors(errors.NOT_USER_FILE, errors.NOT_USER_FILE, errors.NoneError)
	}

	if videoRecord.Status != "uploaded" {
		log.Warn().
			Str("user_ksuid", userKSUID).
			Str("file_ksuid", fileKSUID).
			Str("status", videoRecord.Status).
			Msg("视频文件尚未完全上传")
		return nil, errors.NewGlobalErrors(errors.FILE_NOT_UPLOADED, errors.FILE_NOT_UPLOADED, errors.NoneError)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("file_ksuid", fileKSUID).
		Interface("videoRecord", videoRecord).
		Msg("视频文件信息验证成功")

	return &videoRecord, nil
}

// validateCoverFile 验证封面文件信息
func (s *ContentService) validateCoverFile(ctx context.Context, userKSUID string, coverKSUID string) (string, string, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("cover_ksuid", coverKSUID).
		Msg("开始验证封面文件信息")

	coverResponse, err := s.storageServiceClient.GetCoverInfo(coverKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("cover_ksuid", coverKSUID).
			Msg("调用存储服务获取封面信息失败")
		return "", "", errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.SEND_INTERNAL_HTTP_ERROR, err)
	}

	if coverResponse.Code != 1 {
		log.Error().
			Str("user_ksuid", userKSUID).
			Str("cover_ksuid", coverKSUID).
			Int("response_code", coverResponse.Code).
			Interface("data", coverResponse.Data).
			Msg("存储服务返回封面信息错误")
		return "", "", errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.INTERNAL_HTTP_RESPONSE_ERROR, fmt.Errorf(coverResponse.Message))
	}

	var coverInfo types.CoverInfo
	jsonBytes, err := json.Marshal(coverResponse.Data)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("cover_ksuid", coverKSUID).
			Msg("序列化封面信息失败")
		return "", "", errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.JSON_MARSHAL_ERROR, err)
	}

	err = json.Unmarshal(jsonBytes, &coverInfo)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("cover_ksuid", coverKSUID).
			Msg("反序列化封面信息失败")
		return "", "", errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.JSON_UNMARSHAL_ERROR, err)
	}

	// 验证封面所有者：发布视频的用户必须是上传封面的用户
	if coverInfo.UserKSUID != userKSUID {
		log.Error().
			Str("video_user_ksuid", userKSUID).
			Str("cover_user_ksuid", coverInfo.UserKSUID).
			Str("cover_ksuid", coverKSUID).
			Msg("封面所有者验证失败：发布视频的用户与上传封面的用户不一致")
		return "", "", errors.NewGlobalErrors(errors.PERMISSION_DENIED, errors.PERMISSION_DENIED, fmt.Errorf("无权使用此封面：封面属于其他用户"))
	}

	// 设置封面路径和URL
	coverPath := coverInfo.BucketPath // 存储桶中的路径
	coverURL := coverInfo.FileURL     // 公开访问URL

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("cover_ksuid", coverKSUID).
		Str("cover_user_ksuid", coverInfo.UserKSUID).
		Str("cover_path", coverPath).
		Str("cover_url", coverURL).
		Msg("封面信息验证成功")

	return coverPath, coverURL, nil
}

// enrichContentsWithUserInfo 批量获取用户信息并转换为ContentSimpleModel
func (s *ContentService) enrichContentsSimple(ctx context.Context, contents []*model.Content) ([]*dto.ContentSimpleModel, error) {
	if len(contents) == 0 {
		return []*dto.ContentSimpleModel{}, nil
	}

	// 收集所有唯一的用户KSUID
	userKSUIDs := make([]string, 0, len(contents))
	userKSUIDSet := make(map[string]bool)

	for _, content := range contents {
		if content.UserKSUID != "" && !userKSUIDSet[content.UserKSUID] {
			userKSUIDs = append(userKSUIDs, content.UserKSUID)
			userKSUIDSet[content.UserKSUID] = true
		}
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]*dto.UserBasicInfo)

	if len(userKSUIDs) > 0 {
		log.Info().
			Int("user_count", len(userKSUIDs)).
			Strs("user_ksuids", userKSUIDs).
			Msg("开始批量获取用户信息")

		// 调用用户服务批量获取用户信息
		usersResponse, err := s.userServiceClient.BatchGetUsers(userKSUIDs)
		if err != nil {
			log.Error().
				Err(err).
				Int("user_count", len(userKSUIDs)).
				Msg("调用用户服务批量获取用户信息失败")
			// 不返回错误，继续处理，只是用户信息为空
		} else if usersResponse != nil {
			// 填充用户信息映射
			for _, user := range usersResponse.Users {
				userInfoMap[user.UserKSUID] = &user
			}

			log.Info().
				Int("requested_users", len(userKSUIDs)).
				Int("found_users", len(usersResponse.Users)).
				Msg("批量获取用户信息完成")
		}
	}

	// 转换为包含用户信息的DTO
	result := make([]*dto.ContentSimpleModel, len(contents))
	for i, content := range contents {
		// 转换到ContentSimpleModel
		result[i] = dto.ConvertToContentSimpleModel(content)

		// 如果找到用户信息，则填充
		if userInfo, exists := userInfoMap[content.UserKSUID]; exists {
			result[i].UserInfo = userInfo
		}
	}

	log.Info().
		Int("contents_count", len(contents)).
		Int("users_found", len(userInfoMap)).
		Msg("内容与用户信息组合完成")

	return result, nil
}

// enrichContentDetail 批量获取用户信息并构建内容详情DTO
func (s *ContentService) enrichContentDetail(ctx context.Context, content *model.Content) (*dto.ContentDetailWithUserInfoAndCollaborators, error) {
	// 收集所有需要获取用户信息的KSUID
	userKSUIDs := make([]string, 0)
	userKSUIDSet := make(map[string]bool)

	// 添加内容发布者的用户KSUID
	if !userKSUIDSet[content.UserKSUID] {
		userKSUIDs = append(userKSUIDs, content.UserKSUID)
		userKSUIDSet[content.UserKSUID] = true
	}

	// 添加协作者的用户KSUID
	for _, collaborator := range content.Collaborators {
		if !userKSUIDSet[collaborator.UserKSUID] {
			userKSUIDs = append(userKSUIDs, collaborator.UserKSUID)
			userKSUIDSet[collaborator.UserKSUID] = true
		}
	}

	// 创建用户信息映射
	userInfoMap := make(map[string]*dto.UserBasicInfo)

	if len(userKSUIDs) > 0 {
		log.Info().
			Int("user_count", len(userKSUIDs)).
			Strs("user_ksuids", userKSUIDs).
			Str("content_ksuid", content.ContentKSUID).
			Msg("开始批量获取内容详情相关用户信息")

		// 调用用户服务批量获取用户信息
		usersResponse, err := s.userServiceClient.BatchGetUsers(userKSUIDs)
		if err != nil {
			log.Error().
				Err(err).
				Int("user_count", len(userKSUIDs)).
				Str("content_ksuid", content.ContentKSUID).
				Msg("调用用户服务批量获取用户信息失败")
			// 不返回错误，继续处理，只是用户信息为空
		} else if usersResponse != nil {
			// 填充用户信息映射
			for _, user := range usersResponse.Users {
				userInfoMap[user.UserKSUID] = (*dto.UserBasicInfo)(&user)
			}

			log.Info().
				Int("requested_users", len(userKSUIDs)).
				Int("found_users", len(usersResponse.Users)).
				Str("content_ksuid", content.ContentKSUID).
				Msg("批量获取内容详情相关用户信息完成")
		}
	}

	// 构建内容详情DTO
	contentDetail := &dto.ContentDetailWithUserInfoAndCollaborators{
		ExportContentModel: dto.ContentToExportContentModel(content),
	}

	// 填充内容发布者用户信息
	if userInfo, exists := userInfoMap[content.UserKSUID]; exists {
		contentDetail.UserInfo = userInfo
	}

	contentDetail.Collaborators = make([]*dto.CollaboratorWithUserInfo, len(content.Collaborators))
	// 填充协作者用户信息
	if len(content.Collaborators) > 0 {
		for i, collaborator := range content.Collaborators {
			collaboratorWithUser := &dto.CollaboratorWithUserInfo{
				Collaborator: &collaborator,
			}

			// 如果找到用户信息，则填充. 但实际情况下用户一定存在
			if userInfo, exists := userInfoMap[collaborator.UserKSUID]; exists {
				collaboratorWithUser.UserInfo = userInfo
			}

			contentDetail.Collaborators[i] = collaboratorWithUser
		}
	}

	// 获取收藏数 - 调用交互服务内部API
	favoriteCount, err := s.interactionServiceClient.GetContentFavoriteCount(content.ContentKSUID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("content_ksuid", content.ContentKSUID).
			Msg("获取内容收藏数失败")
		// 不返回错误，继续处理，收藏数保持原值
	} else {
		// 更新收藏数到内容详情中
		contentDetail.FavoriteCount = favoriteCount
	}

	// 获取点赞统计 - 调用交互服务内部API
	likeCount, dislikeCount, err := s.interactionServiceClient.GetContentLikeCounts(content.ContentKSUID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("content_ksuid", content.ContentKSUID).
			Msg("获取内容点赞统计失败")
		// 不返回错误，继续处理，点赞数保持原值
	} else {
		// 更新点赞统计到内容详情中
		contentDetail.LikeCount = likeCount
		contentDetail.DislikeCount = dislikeCount
	}

	log.Info().
		Str("content_ksuid", content.ContentKSUID).
		Int("total_users", len(userKSUIDs)).
		Int("found_users", len(userInfoMap)).
		Int("collaborators_count", len(contentDetail.Collaborators)).
		Int64("favorite_count", contentDetail.FavoriteCount).
		Int64("like_count", contentDetail.LikeCount).
		Int64("dislike_count", contentDetail.DislikeCount).
		Msg("内容详情与用户信息组合完成")

	return contentDetail, nil
}

// GetContentDetailWithUserInteraction 获取包含用户交互状态的内容详情
func (s *ContentService) GetContentDetailWithUserInteraction(ctx context.Context, contentKSUID, userKSUID string) (*dto.ContentDetailResponse, *errors.Errors) {
	log.Info().
		Str("content_ksuid", contentKSUID).
		Str("user_ksuid", userKSUID).
		Msg("开始获取包含用户交互状态的内容详情")

	content, err := s.contentRepo.GetContentByContentKSUID(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容详情失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 处理URL生成逻辑
	if s.urlGenerator != nil {
		if err := s.processContentURLs(ctx, content); err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("处理内容URL失败")
			// 不返回错误，继续返回内容，只是URL可能为空
		}
	}

	// 批量获取用户信息并构建详情DTO，传入用户KSUID以获取收藏状态
	contentDetail, err := s.enrichContentDetail(ctx, content)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("批量获取用户信息失败")
		// 不返回错误，继续返回内容，只是用户信息可能为空
	}

	// 构建响应，包含用户交互状态
	response := &dto.ContentDetailResponse{
		ExportContentModel: contentDetail.ExportContentModel,
		UserInfo:           contentDetail.UserInfo,
		Collaborators:      contentDetail.Collaborators,
	}

	// 获取用户收藏状态
	if userKSUID != "" {
		isFavorited, err := s.interactionServiceClient.CheckFavoriteStatus(userKSUID, contentKSUID)
		if err != nil {
			log.Warn().
				Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", contentKSUID).
				Msg("检查用户收藏状态失败")
		} else {
			response.IsFavorited = isFavorited
		}
		// 获取用户点赞状态（统一方法）
		likeType, exists, err := s.interactionServiceClient.CheckUserLikeStatus(userKSUID, contentKSUID)
		if err != nil {
			log.Warn().
				Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_id", contentKSUID).
				Msg("检查用户点赞状态失败")
		} else if exists {
			if likeType == "like" {
				response.IsLiked = true
			} else if likeType == "dislike" {
				response.IsDisliked = true
			}
		}
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Str("user_ksuid", userKSUID).
		Bool("is_favorited", response.IsFavorited).
		Int64("favorite_count", response.FavoriteCount).
		Msg("获取包含用户交互状态的内容详情完成")

	return response, nil
}
