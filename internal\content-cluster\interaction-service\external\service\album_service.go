package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	dto2 "pxpat-backend/internal/content-cluster/interaction-service/dto"
	model2 "pxpat-backend/internal/content-cluster/interaction-service/model"
	repository2 "pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
)

type AlbumService struct {
	albumRepository        *repository2.AlbumRepository
	albumContentRepository *repository2.AlbumContentRepository
	storageClient          client.ContentStorageServiceClient
	db                     *gorm.DB
}

func NewAlbumService(
	albumRepository *repository2.AlbumRepository,
	albumContentRepository *repository2.AlbumContentRepository,
	storageClient client.ContentStorageServiceClient,
	db *gorm.DB,
) *AlbumService {
	return &AlbumService{
		albumRepository:        albumRepository,
		albumContentRepository: albumContentRepository,
		storageClient:          storageClient,
		db:                     db,
	}
}

// CreateAlbum 创建合集
func (s *AlbumService) CreateAlbum(ctx context.Context, req *dto2.CreateAlbumRequest) (*dto2.CreateAlbumResponse, *errors.Errors) {
	// 验证合集类型
	if !model2.IsValidAlbumType(req.AlbumType) {
		return nil, errors.New(errors.INVALID_PARAMS, "无效的合集类型")
	}

	// 检查用户合集数量限制
	albumCount, err := s.albumRepository.GetUserAlbumCount(ctx, req.UserKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", req.UserKSUID).Msg("获取用户合集数量失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取用户合集数量失败")
	}
	if albumCount >= 100 { // 配置中的最大合集数
		return nil, errors.New(errors.ALBUM_LIMIT_EXCEEDED, "用户合集数量已达上限")
	}

	// 检查合集名称是否重复
	nameExists, err := s.albumRepository.CheckAlbumNameExists(ctx, req.UserKSUID, req.AlbumName, "")
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", req.UserKSUID).Str("album_name", req.AlbumName).Msg("检查合集名称重复失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查合集名称失败")
	}
	if nameExists {
		return nil, errors.New(errors.ALBUM_NAME_EXISTS, "合集名称已存在")
	}

	// 验证封面信息（如果提供了封面KSUID）
	var coverURL string
	if req.CoverKSUID != "" && strings.TrimSpace(req.CoverKSUID) != "" {
		coverInfo, err := s.storageClient.GetCoverInfo(req.CoverKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", req.UserKSUID).
				Str("cover_ksuid", req.CoverKSUID).
				Msg("获取封面信息失败")
			return nil, errors.New(errors.FILE_NOT_FOUND, "封面不存在")
		}

		// 验证封面是否属于当前用户
		if coverInfo.UserKSUID != req.UserKSUID {
			log.Warn().
				Str("user_ksuid", req.UserKSUID).
				Str("cover_ksuid", req.CoverKSUID).
				Str("cover_owner", coverInfo.UserKSUID).
				Msg("用户尝试使用不属于自己的封面")
			return nil, errors.New(errors.PERMISSION_DENIED, "无权使用该封面")
		}

		// 验证封面状态是否可用
		if coverInfo.Status != "uploaded" && coverInfo.Status != "active" {
			log.Warn().
				Str("user_ksuid", req.UserKSUID).
				Str("cover_ksuid", req.CoverKSUID).
				Str("cover_status", coverInfo.Status).
				Msg("封面状态不可用")
			return nil, errors.New(errors.FILE_NOT_AVAILABLE, "封面状态不可用")
		}

		coverURL = coverInfo.FileURL
		log.Info().
			Str("user_ksuid", req.UserKSUID).
			Str("cover_ksuid", req.CoverKSUID).
			Str("cover_url", coverURL).
			Msg("封面验证成功")
	}

	// 创建合集
	album := &model2.Album{
		AlbumKSUID:   ksuid.GenerateKSUID(),
		UserKSUID:    req.UserKSUID,
		AlbumName:    req.AlbumName,
		AlbumType:    req.AlbumType,
		CoverKSUID:   req.CoverKSUID,
		CoverURL:     coverURL,
		Description:  req.Description,
		IsPublic:     req.IsPublic,
		ContentCount: 0,
		MarkCount:    0,
		ShareCount:   0,
		SortOrder:    0,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err = s.albumRepository.CreateAlbum(ctx, album)
	if err != nil {
		log.Error().Err(err).Interface("album", album).Msg("创建合集失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "创建合集失败")
	}

	log.Info().Str("album_ksuid", album.AlbumKSUID).Str("user_ksuid", req.UserKSUID).Msg("合集创建成功")

	return &dto2.CreateAlbumResponse{
		AlbumKSUID:   album.AlbumKSUID,
		AlbumName:    album.AlbumName,
		AlbumType:    album.AlbumType,
		CoverKSUID:   album.CoverKSUID,
		CoverURL:     album.CoverURL,
		IsPublic:     album.IsPublic,
		Description:  album.Description,
		ContentCount: album.ContentCount,
		Contents:     []dto2.AlbumContentItem{}, // 新创建的合集没有内容
		CreatedAt:    album.CreatedAt,
	}, nil
}

// GetAlbum 获取合集信息
func (s *AlbumService) GetAlbum(ctx context.Context, req *dto2.GetAlbumRequest) (*dto2.GetAlbumResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：如果是私有合集，只有创建者可以查看
	if !album.IsPublic && req.UserKSUID != album.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限查看此合集")
	}

	// 获取合集内容列表
	albumContents, _, err := s.albumContentRepository.GetAlbumContents(ctx, album.AlbumKSUID, 0, 20, "sort_order", "asc")
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", album.AlbumKSUID).Msg("获取合集内容失败")
		albumContents = []model2.AlbumContent{}
	}

	// 转换内容格式
	contentItems := make([]dto2.AlbumContentItem, len(albumContents))
	for i, content := range albumContents {
		contentItems[i] = dto2.AlbumContentItem{
			ID:           content.ID,
			AlbumKSUID:   content.AlbumKSUID,
			ContentKSUID: content.ContentKSUID,
			ContentType:  content.ContentType,
			SortOrder:    content.SortOrder,
			AddedAt:      content.CreatedAt,
			// 基础信息，详细信息需要通过其他接口获取
			Title:     "",
			CoverURL:  "",
			Duration:  0,
			ViewCount: 0,
			LikeCount: 0,
		}
	}

	return &dto2.GetAlbumResponse{
		AlbumKSUID:   album.AlbumKSUID,
		UserKSUID:    album.UserKSUID,
		AlbumName:    album.AlbumName,
		AlbumType:    album.AlbumType,
		IsPublic:     album.IsPublic,
		Description:  album.Description,
		ContentCount: album.ContentCount,
		MarkCount:    album.MarkCount,
		ShareCount:   album.ShareCount,
		Contents:     contentItems,
		CreatedAt:    album.CreatedAt,
		UpdatedAt:    album.UpdatedAt,
	}, nil
}

// UpdateAlbum 更新合集信息
func (s *AlbumService) UpdateAlbum(ctx context.Context, req *dto2.UpdateAlbumRequest) (*dto2.UpdateAlbumResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以修改
	if album.UserKSUID != req.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限修改此合集")
	}

	// 检查合集名称是否重复（排除当前合集）
	nameExists, err := s.albumRepository.CheckAlbumNameExists(ctx, req.UserKSUID, req.AlbumName, req.AlbumKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", req.UserKSUID).Str("album_name", req.AlbumName).Msg("检查合集名称重复失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查合集名称失败")
	}
	if nameExists {
		return nil, errors.New(errors.ALBUM_NAME_EXISTS, "合集名称已存在")
	}

	// 更新合集信息
	album.AlbumName = req.AlbumName
	album.Description = req.Description
	album.IsPublic = req.IsPublic
	album.UpdatedAt = time.Now()

	err = s.albumRepository.UpdateAlbum(ctx, album)
	if err != nil {
		log.Error().Err(err).Interface("album", album).Msg("更新合集失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "更新合集失败")
	}

	// 获取合集内容列表
	albumContents, _, err := s.albumContentRepository.GetAlbumContents(ctx, album.AlbumKSUID, 0, 20, "sort_order", "asc")
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", album.AlbumKSUID).Msg("获取合集内容失败")
		albumContents = []model2.AlbumContent{}
	}

	// 转换内容格式
	contentItems := make([]dto2.AlbumContentItem, len(albumContents))
	for i, content := range albumContents {
		contentItems[i] = dto2.AlbumContentItem{
			ID:           content.ID,
			AlbumKSUID:   content.AlbumKSUID,
			ContentKSUID: content.ContentKSUID,
			ContentType:  content.ContentType,
			SortOrder:    content.SortOrder,
			AddedAt:      content.CreatedAt,
			// 基础信息，详细信息需要通过其他接口获取
			Title:     "",
			CoverURL:  "",
			Duration:  0,
			ViewCount: 0,
			LikeCount: 0,
		}
	}

	log.Info().Str("album_ksuid", album.AlbumKSUID).Str("user_ksuid", req.UserKSUID).Msg("合集更新成功")

	return &dto2.UpdateAlbumResponse{
		AlbumKSUID:   album.AlbumKSUID,
		AlbumName:    album.AlbumName,
		AlbumType:    album.AlbumType,
		IsPublic:     album.IsPublic,
		Description:  album.Description,
		ContentCount: album.ContentCount,
		Contents:     contentItems,
		UpdatedAt:    album.UpdatedAt,
	}, nil
}

// DeleteAlbum 删除合集
func (s *AlbumService) DeleteAlbum(ctx context.Context, req *dto2.DeleteAlbumRequest) *errors.Errors {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以删除
	if album.UserKSUID != req.UserKSUID {
		return errors.New(errors.PERMISSION_DENIED, "无权限删除此合集")
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除合集内容关联记录
	err = s.albumContentRepository.DeleteAlbumContents(ctx, req.AlbumKSUID)
	if err != nil {
		tx.Rollback()
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("删除合集内容关联失败")
		return errors.New(errors.INTERNAL_ERROR, "删除合集内容关联失败")
	}

	// 软删除合集
	err = s.albumRepository.DeleteAlbum(ctx, req.AlbumKSUID)
	if err != nil {
		tx.Rollback()
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("删除合集失败")
		return errors.New(errors.INTERNAL_ERROR, "删除合集失败")
	}

	tx.Commit()

	log.Info().Str("album_ksuid", req.AlbumKSUID).Str("user_ksuid", req.UserKSUID).Msg("合集删除成功")
	return nil
}

// GetUserAlbums 获取用户合集列表
func (s *AlbumService) GetUserAlbums(ctx context.Context, req *dto2.GetUserAlbumsRequest) (*dto2.GetUserAlbumsResponse, *errors.Errors) {
	// 权限检查：是否可以查看私有合集
	includePrivate := req.RequestUserKSUID == req.UserKSUID

	// 计算分页参数
	offset := (req.Page - 1) * req.PageSize

	// 获取用户合集列表
	albums, total, err := s.albumRepository.GetUserAlbums(ctx, req.UserKSUID, req.AlbumType, offset, req.PageSize, includePrivate)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", req.UserKSUID).Msg("获取用户合集列表失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取用户合集列表失败")
	}

	// 转换为响应格式
	albumItems := make([]dto2.UserAlbumItem, len(albums))
	for i, album := range albums {
		albumItems[i] = dto2.UserAlbumItem{
			AlbumKSUID:   album.AlbumKSUID,
			AlbumName:    album.AlbumName,
			AlbumType:    album.AlbumType,
			IsPublic:     album.IsPublic,
			Description:  album.Description,
			ContentCount: album.ContentCount,
			CreatedAt:    album.CreatedAt,
			UpdatedAt:    album.UpdatedAt,
		}
	}

	return &dto2.GetUserAlbumsResponse{
		Albums:   albumItems,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// BatchAddContents 批量添加内容到合集
func (s *AlbumService) BatchAddContents(ctx context.Context, req *dto2.BatchAddContentsToAlbumRequest) (*dto2.BatchOperationResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以添加内容
	if album.UserKSUID != req.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限修改此合集")
	}

	// 验证所有内容类型
	for _, content := range req.Contents {
		if album.AlbumType != content.ContentType {
			return nil, errors.New(errors.CONTENT_TYPE_MISMATCH, fmt.Sprintf("内容 %s 类型与合集类型不匹配", content.ContentKSUID))
		}
	}

	// 准备批量添加内容

	// 本地处理批量添加内容
	var successCount, failedCount int
	var failedItems []dto2.BatchFailedItem

	// 获取当前合集的最大排序值
	maxSortOrder, err := s.albumContentRepository.GetMaxSortOrder(ctx, req.AlbumKSUID)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取最大排序值失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取排序信息失败")
	}

	var albumContents []model2.AlbumContent
	currentSortOrder := maxSortOrder

	for _, contentItem := range req.Contents {
		// 验证内容类型是否与合集类型匹配
		if album.AlbumType != contentItem.ContentType {
			failedCount++
			failedItems = append(failedItems, dto2.BatchFailedItem{
				ContentKSUID: contentItem.ContentKSUID,
				Error:        "内容类型与合集类型不匹配",
			})
			continue
		}

		// 检查内容是否已在合集中
		existing, err := s.albumContentRepository.CheckContentInAlbum(ctx, req.AlbumKSUID, contentItem.ContentKSUID)
		if err == nil && existing != nil {
			failedCount++
			failedItems = append(failedItems, dto2.BatchFailedItem{
				ContentKSUID: contentItem.ContentKSUID,
				Error:        "内容已在合集中",
			})
			continue
		}

		// 确定排序顺序
		sortOrder := contentItem.SortOrder
		if sortOrder == 0 {
			currentSortOrder++
			sortOrder = currentSortOrder
		}

		// 创建合集内容关联记录
		albumContent := model2.AlbumContent{
			AlbumKSUID:   req.AlbumKSUID,
			ContentKSUID: contentItem.ContentKSUID,
			ContentType:  contentItem.ContentType,
			SortOrder:    sortOrder,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		albumContents = append(albumContents, albumContent)
		successCount++
	}

	// 批量插入
	if len(albumContents) > 0 {
		err = s.albumContentRepository.BatchAddContentsToAlbum(ctx, albumContents)
		if err != nil {
			log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("批量添加内容到合集失败")
			return nil, errors.New(errors.INTERNAL_ERROR, "批量添加内容到合集失败")
		}
	}

	// 更新合集内容数量
	if successCount > 0 {
		err = s.albumRepository.IncrementAlbumContentCount(ctx, req.AlbumKSUID, successCount)
		if err != nil {
			log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Int("count", successCount).Msg("更新合集内容数量失败")
			// 不返回错误，只记录日志
		}
	}

	// 获取更新后的合集信息
	updatedAlbum, gErr := s.GetAlbum(ctx, &dto2.GetAlbumRequest{
		UserKSUID:  req.UserKSUID,
		AlbumKSUID: req.AlbumKSUID,
	})
	if gErr != nil {
		log.Error().Err(gErr.Err).Str("album_ksuid", req.AlbumKSUID).Msg("获取更新后的合集信息失败")
		updatedAlbum = nil
	}

	log.Info().Str("album_ksuid", req.AlbumKSUID).Int("success_count", successCount).Int("failed_count", failedCount).Msg("批量添加内容完成")

	return &dto2.BatchOperationResponse{
		AlbumKSUID:   req.AlbumKSUID,
		SuccessCount: successCount,
		FailedCount:  failedCount,
		FailedItems:  failedItems,
		UpdatedAlbum: updatedAlbum,
	}, nil
}

// BatchRemoveContents 批量从合集移除内容
func (s *AlbumService) BatchRemoveContents(ctx context.Context, req *dto2.BatchRemoveContentsFromAlbumRequest) (*dto2.BatchOperationResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以移除内容
	if album.UserKSUID != req.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限修改此合集")
	}

	// 本地处理批量移除内容
	// 检查哪些内容在合集中
	existingContents, err := s.albumContentRepository.GetContentsByKSUIDs(ctx, req.AlbumKSUID, req.ContentKSUIDs)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("检查内容是否在合集中失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查内容状态失败")
	}

	// 创建存在的内容KSUID映射
	existingMap := make(map[string]bool)
	for _, content := range existingContents {
		existingMap[content.ContentKSUID] = true
	}

	var successKSUIDs []string
	var failedItems []dto2.BatchFailedItem

	for _, contentKSUID := range req.ContentKSUIDs {
		if existingMap[contentKSUID] {
			successKSUIDs = append(successKSUIDs, contentKSUID)
		} else {
			failedItems = append(failedItems, dto2.BatchFailedItem{
				ContentKSUID: contentKSUID,
				Error:        "内容不在合集中",
			})
		}
	}

	// 批量移除
	successCount := 0
	if len(successKSUIDs) > 0 {
		err = s.albumContentRepository.BatchRemoveContentsFromAlbum(ctx, req.AlbumKSUID, successKSUIDs)
		if err != nil {
			log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("批量从合集移除内容失败")
			return nil, errors.New(errors.INTERNAL_ERROR, "批量从合集移除内容失败")
		}
		successCount = len(successKSUIDs)
	}

	failedCount := len(failedItems)

	// 更新合集内容数量
	if successCount > 0 {
		err = s.albumRepository.IncrementAlbumContentCount(ctx, req.AlbumKSUID, -successCount)
		if err != nil {
			log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Int("count", -successCount).Msg("更新合集内容数量失败")
			// 不返回错误，只记录日志
		}
	}

	// 获取更新后的合集信息
	updatedAlbum, gErr := s.GetAlbum(ctx, &dto2.GetAlbumRequest{
		UserKSUID:  req.UserKSUID,
		AlbumKSUID: req.AlbumKSUID,
	})
	if gErr != nil {
		log.Error().Err(gErr.Err).Str("album_ksuid", req.AlbumKSUID).Msg("获取更新后的合集信息失败")
		updatedAlbum = nil
	}

	log.Info().Str("album_ksuid", req.AlbumKSUID).Int("success_count", successCount).Int("failed_count", failedCount).Msg("批量移除内容完成")

	return &dto2.BatchOperationResponse{
		AlbumKSUID:   req.AlbumKSUID,
		SuccessCount: successCount,
		FailedCount:  failedCount,
		FailedItems:  failedItems,
		UpdatedAlbum: updatedAlbum,
	}, nil
}

// UpdateContentSortOrder 更新内容在合集中的排序
func (s *AlbumService) UpdateContentSortOrder(ctx context.Context, req *dto2.UpdateContentSortOrderRequest) *errors.Errors {
	// 获取合集信息并检查权限
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：只有创建者可以更新排序
	if album.UserKSUID != req.UserKSUID {
		return errors.New(errors.PERMISSION_DENIED, "无权限修改此合集")
	}

	// 检查内容是否在合集中
	existing, err := s.albumContentRepository.CheckContentInAlbum(ctx, req.AlbumKSUID, req.ContentKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New(errors.CONTENT_NOT_IN_ALBUM, "内容不在合集中")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Msg("检查内容是否在合集中失败")
		return errors.New(errors.INTERNAL_ERROR, "检查内容状态失败")
	}

	if existing == nil {
		return errors.New(errors.CONTENT_NOT_IN_ALBUM, "内容不在合集中")
	}

	// 更新排序
	err = s.albumContentRepository.UpdateContentSortOrder(ctx, req.AlbumKSUID, req.ContentKSUID, req.SortOrder)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Int("sort_order", req.SortOrder).Msg("更新内容排序失败")
		return errors.New(errors.INTERNAL_ERROR, "更新内容排序失败")
	}

	log.Info().Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Int("sort_order", req.SortOrder).Msg("内容排序更新成功")
	return nil
}

// CheckContentInAlbum 检查内容是否在合集中
func (s *AlbumService) CheckContentInAlbum(ctx context.Context, req *dto2.CheckContentInAlbumRequest) (*dto2.CheckContentInAlbumResponse, *errors.Errors) {
	albumContent, err := s.albumContentRepository.CheckContentInAlbum(ctx, req.AlbumKSUID, req.ContentKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dto2.CheckContentInAlbumResponse{
				Exists: false,
			}, nil
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Str("content_ksuid", req.ContentKSUID).Msg("检查内容是否在合集中失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "检查内容状态失败")
	}

	if albumContent == nil {
		return &dto2.CheckContentInAlbumResponse{
			Exists: false,
		}, nil
	}

	return &dto2.CheckContentInAlbumResponse{
		Exists:    true,
		SortOrder: albumContent.SortOrder,
		AddedAt:   albumContent.CreatedAt,
	}, nil
}

// ==================== 辅助方法 ====================

// ==================== 合集内容管理方法 ====================

// GetAlbumContents 获取合集内容列表
func (s *AlbumService) GetAlbumContents(ctx context.Context, req *dto2.GetAlbumContentsRequest) (*dto2.GetAlbumContentsResponse, *errors.Errors) {
	// 获取合集信息
	album, err := s.albumRepository.GetAlbumByKSUID(ctx, req.AlbumKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(errors.ALBUM_NOT_FOUND, "合集不存在")
		}
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集信息失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集信息失败")
	}

	// 权限检查：如果是私有合集，只有创建者可以查看
	if !album.IsPublic && req.UserKSUID != album.UserKSUID {
		return nil, errors.New(errors.PERMISSION_DENIED, "无权限查看此合集")
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}
	if req.SortBy == "" {
		req.SortBy = "sort_order"
	}
	if req.SortOrder == "" {
		req.SortOrder = "asc"
	}

	// 计算分页参数
	offset := (req.Page - 1) * req.PageSize

	// 获取合集内容列表
	albumContents, total, err := s.albumContentRepository.GetAlbumContents(ctx, req.AlbumKSUID, offset, req.PageSize, req.SortBy, req.SortOrder)
	if err != nil {
		log.Error().Err(err).Str("album_ksuid", req.AlbumKSUID).Msg("获取合集内容列表失败")
		return nil, errors.New(errors.INTERNAL_ERROR, "获取合集内容列表失败")
	}

	// 转换为响应格式
	contentItems := make([]dto2.AlbumContentItem, len(albumContents))
	for i, albumContent := range albumContents {
		contentItems[i] = dto2.AlbumContentItem{
			ID:           albumContent.ID,
			AlbumKSUID:   albumContent.AlbumKSUID,
			ContentKSUID: albumContent.ContentKSUID,
			ContentType:  albumContent.ContentType,
			SortOrder:    albumContent.SortOrder,
			AddedAt:      albumContent.CreatedAt,
			// 基础信息，详细信息需要通过其他接口获取
			Title:     "",
			CoverURL:  "",
			Duration:  0,
			ViewCount: 0,
			LikeCount: 0,
		}
	}

	return &dto2.GetAlbumContentsResponse{
		Contents: contentItems,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}
