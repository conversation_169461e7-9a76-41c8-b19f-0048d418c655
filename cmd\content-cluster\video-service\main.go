package main

import (
	"context"

	"pxpat-backend/cmd"
	"pxpat-backend/cmd/content-cluster/video-service/providers"
	"pxpat-backend/internal/content-cluster/video-service/client"
	externalService "pxpat-backend/internal/content-cluster/video-service/external/service"
	intraService "pxpat-backend/internal/content-cluster/video-service/intra/service"
	"pxpat-backend/internal/content-cluster/video-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	"pxpat-backend/internal/content-cluster/video-service/types"
	"pxpat-backend/pkg/consul"
	pkgMessaging "pxpat-backend/pkg/messaging"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
)



// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	mqPublisher *publisher.Publisher,
	mqMultiConsumer *pkgMessaging.MultiConsumer,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(_ context.Context) error {
			ctx := context.Background()
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动MQ多消费者（在goroutine中，使用新的context避免deadline问题）
			if mqMultiConsumer != nil {
				go func() {
					// 创建一个新的context，不继承fx OnStart的deadline
					log.Info().Msg("Starting MQ multi consumer")
					if err := mqMultiConsumer.StartConsuming(ctx); err != nil {
						log.Error().Err(err).Msg("Error starting MQ multi consumer")
					}
				}()
			}

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting video service server")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭MQ多消费者
			if mqMultiConsumer != nil {
				if err := mqMultiConsumer.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ多消费者失败")
				} else {
					log.Info().Msg("MQ多消费者关闭成功")
				}
			}

			// 关闭MQ发布器
			if mqPublisher != nil {
				if err := mqPublisher.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ发布器失败")
				} else {
					log.Info().Msg("MQ发布器关闭成功")
				}
			}

			log.Info().Msg("Video service stopped")
			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 基础设施层
		fx.Provide(
			providers.ProvideConfig,
			providers.ProvideDatabase,
			providers.ProvideRedis,
			providers.ProvideCacheManager,
			providers.ProvideConsulManager,
			providers.ProvideHealthHandler,
			providers.ProvideJWTManager,
		),

		// 客户端层
		fx.Provide(
			client.ProvideUserServiceClient,
			client.ProvideInteractionServiceClient,
			client.ProvideAuditServiceClient,
			client.ProvideStorageClient,
		),

		// 消息队列层
		fx.Provide(
			providers.ProvideMQPublisher,
			providers.ProvideMQMultiConsumer,
		),

		// 存储层
		fx.Provide(
			repository.ProvideRepositories,
		),

		// 业务层
		fx.Provide(
			externalService.ProvideExternalServices,
			intraService.ProvideInternalServices,
		),

		// 处理器层
		fx.Provide(
			providers.ProvidePermissionMiddleware,
			providers.ProvideGinEngine,
		),

		// 生命周期管理
		fx.Invoke(
			providers.ProvideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
