package providers

import (
	"pxpat-backend/internal/content-cluster/audit-service/types"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/logger"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// ProvideConfig 提供配置
func ProvideConfig() *types.Config {
	clusterName := "content"
	serviceName := "audit"

	config := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
	
	log.Info().Msg("Configuration loaded successfully")
	return config
}

// ProvideLogger 提供日志器
func ProvideLogger(cfg *types.Config) zerolog.Logger {
	serviceName := "audit"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("Logger initialized successfully")
	log.Info().Msg("Audit service starting...")
	return log.Logger
}
