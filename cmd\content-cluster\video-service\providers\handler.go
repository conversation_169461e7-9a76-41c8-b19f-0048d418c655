package providers

import (
	"context"
	"fmt"

	"pxpat-backend/internal/content-cluster/video-service/middleware"
	"pxpat-backend/internal/content-cluster/video-service/routes"
	"pxpat-backend/internal/content-cluster/video-service/types"
	externalService "pxpat-backend/internal/content-cluster/video-service/external/service"
	intraService "pxpat-backend/internal/content-cluster/video-service/intra/service"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/cache"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// userServiceClientAdapter 用户服务客户端适配器
type userServiceClientAdapter struct {
	baseURL string
}

// GetUserLevel 获取用户等级信息
func (a *userServiceClientAdapter) GetUserLevel(ctx context.Context, userID string) (*middleware.UserLevelInfo, error) {
	// 这里应该实现实际的HTTP调用，暂时返回默认值
	return &middleware.UserLevelInfo{
		UserLevel: 3,
		PCLevel:   1,
		Region:    "CN",
	}, nil
}

// GetUserRoles 获取用户角色
func (a *userServiceClientAdapter) GetUserRoles(ctx context.Context, userID string) ([]middleware.Role, error) {
	// 这里应该实现实际的HTTP调用，暂时返回默认值
	return []middleware.Role{
		{
			ID:       1,
			Type:     "creator",
			Level:    3,
			Region:   "CN",
			IsActive: true,
		},
	}, nil
}

// GetUserPermissions 获取用户权限
func (a *userServiceClientAdapter) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	// 这里应该实现实际的HTTP调用，暂时返回默认权限
	return []string{"content:create", "content:publish", "content:edit"}, nil
}

// ProvidePermissionMiddleware 提供权限中间件
func ProvidePermissionMiddleware(
	cacheManager cache.Manager,
	cfg *types.Config,
) *middleware.PermissionMiddleware {
	userServiceURL := fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port)
	// 创建一个简单的用户服务客户端适配器
	userServiceClient := &userServiceClientAdapter{
		baseURL: userServiceURL,
	}
	permissionMiddleware := middleware.NewPermissionMiddleware(userServiceClient, cacheManager, userServiceURL)
	log.Info().Msg("Permission middleware initialized successfully")
	return permissionMiddleware
}

// ProvideGinEngine 提供Gin引擎并注册路由
func ProvideGinEngine(
	cfg *types.Config,
	healthHandler *consul.HealthHandler,
	// External services
	contentService *externalService.ContentService,
	categoryService *externalService.CategoryService,
	tagService *externalService.TagService,
	commentService *externalService.CommentService,
	complaintService *externalService.ComplaintService,
	collaborationService *externalService.CollaborationService,
	publishStatsService *externalService.PublishStatsService,
	// Internal services
	internalContentService *intraService.ContentService,
	internalCategoryService *intraService.CategoryService,
	internalTagService *intraService.TagService,
	internalCommentService *intraService.CommentService,
	// Middleware and auth
	permissionMiddleware *middleware.PermissionMiddleware,
	jwtManager *auth.Manager,
) *gin.Engine {
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	router := gin.Default()
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 注册业务路由
	routes.RegisterRoutes(
		router,
		contentService,
		categoryService,
		tagService,
		commentService,
		complaintService,
		collaborationService,
		publishStatsService,
		permissionMiddleware,
		jwtManager,
		internalContentService,
		internalCategoryService,
		internalTagService,
		internalCommentService,
	)

	log.Info().Msg("Routes registered successfully")
	return router
}
